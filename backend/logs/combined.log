{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-05-16 09:21:25"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships with TTL: 300s","service":"mabourse-api","timestamp":"2025-05-16 09:21:26"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/2"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships/2","service":"mabourse-api","timestamp":"2025-05-16 09:22:56"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-05-16 09:30:29"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships with TTL: 300s","service":"mabourse-api","timestamp":"2025-05-16 09:30:29"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships/search?page=1&limit=9","service":"mabourse-api","timestamp":"2025-05-16 09:30:51"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships/search?page=1&limit=9 with TTL: 300s","service":"mabourse-api","timestamp":"2025-05-16 09:30:51"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/2"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships/2","service":"mabourse-api","timestamp":"2025-05-16 09:31:08"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-05-16 10:16:12"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships with TTL: 300s","service":"mabourse-api","timestamp":"2025-05-16 10:16:12"}
{"category":"cache","data":{"action":"count","model":"Admin"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: Admin:count:undefined","service":"mabourse-api","timestamp":"2025-05-16 10:20:44"}
{"category":"cache","data":{"action":"count","model":"Admin"},"level":"debug","message":"[CACHE:SET] Cache set for key: Admin:count:undefined with TTL: 60s","service":"mabourse-api","timestamp":"2025-05-16 10:20:44"}
{"category":"cache","data":{"action":"count","model":"User"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: User:count:undefined","service":"mabourse-api","timestamp":"2025-05-16 10:20:44"}
{"category":"cache","data":{"action":"count","model":"User"},"level":"debug","message":"[CACHE:SET] Cache set for key: User:count:undefined with TTL: 60s","service":"mabourse-api","timestamp":"2025-05-16 10:20:44"}
{"category":"cache","data":{"action":"count","model":"Scholarship"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: Scholarship:count:undefined","service":"mabourse-api","timestamp":"2025-05-16 10:20:44"}
{"category":"cache","data":{"action":"count","model":"Scholarship"},"level":"debug","message":"[CACHE:SET] Cache set for key: Scholarship:count:undefined with TTL: 60s","service":"mabourse-api","timestamp":"2025-05-16 10:20:44"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/health","service":"mabourse-api","timestamp":"2025-05-16 10:20:56"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/health with TTL: 60s","service":"mabourse-api","timestamp":"2025-05-16 10:20:56"}
{"category":"cache","data":{"action":"count","model":"Admin"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: Admin:count:undefined","service":"mabourse-api","timestamp":"2025-05-16 10:21:03"}
{"category":"cache","data":{"action":"count","model":"User"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: User:count:undefined","service":"mabourse-api","timestamp":"2025-05-16 10:21:03"}
{"category":"cache","data":{"action":"count","model":"Scholarship"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: Scholarship:count:undefined","service":"mabourse-api","timestamp":"2025-05-16 10:21:03"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-05-16 10:22:35"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships with TTL: 300s","service":"mabourse-api","timestamp":"2025-05-16 10:22:36"}
{"category":"cache","data":{"action":"count","model":"Admin"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: Admin:count:undefined","service":"mabourse-api","timestamp":"2025-05-16 10:27:07"}
{"category":"cache","data":{"action":"count","model":"Admin"},"level":"debug","message":"[CACHE:SET] Cache set for key: Admin:count:undefined with TTL: 60s","service":"mabourse-api","timestamp":"2025-05-16 10:27:07"}
{"category":"cache","data":{"action":"count","model":"User"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: User:count:undefined","service":"mabourse-api","timestamp":"2025-05-16 10:27:07"}
{"category":"cache","data":{"action":"count","model":"User"},"level":"debug","message":"[CACHE:SET] Cache set for key: User:count:undefined with TTL: 60s","service":"mabourse-api","timestamp":"2025-05-16 10:27:07"}
{"category":"cache","data":{"action":"count","model":"Scholarship"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: Scholarship:count:undefined","service":"mabourse-api","timestamp":"2025-05-16 10:27:07"}
{"category":"cache","data":{"action":"count","model":"Scholarship"},"level":"debug","message":"[CACHE:SET] Cache set for key: Scholarship:count:undefined with TTL: 60s","service":"mabourse-api","timestamp":"2025-05-16 10:27:07"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-05-16 10:28:45"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships with TTL: 300s","service":"mabourse-api","timestamp":"2025-05-16 10:28:45"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/health","service":"mabourse-api","timestamp":"2025-05-16 10:30:06"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/health with TTL: 60s","service":"mabourse-api","timestamp":"2025-05-16 10:30:06"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/health","service":"mabourse-api","timestamp":"2025-05-16 10:32:41"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/health with TTL: 60s","service":"mabourse-api","timestamp":"2025-05-16 10:32:41"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/health","service":"mabourse-api","timestamp":"2025-05-16 10:33:57"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/health with TTL: 60s","service":"mabourse-api","timestamp":"2025-05-16 10:33:57"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-05-16 10:33:58"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships with TTL: 300s","service":"mabourse-api","timestamp":"2025-05-16 10:33:58"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships/search?page=1&limit=9","service":"mabourse-api","timestamp":"2025-05-25 18:18:41"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships/search?page=1&limit=9 with TTL: 300s","service":"mabourse-api","timestamp":"2025-05-25 18:18:42"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-05-25 18:19:00"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships with TTL: 300s","service":"mabourse-api","timestamp":"2025-05-25 18:19:00"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/health","service":"mabourse-api","timestamp":"2025-05-26 17:03:09"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/health with TTL: 60s","service":"mabourse-api","timestamp":"2025-05-26 17:03:10"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-05-26 17:03:10"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships with TTL: 300s","service":"mabourse-api","timestamp":"2025-05-26 17:03:10"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships/search?page=1&limit=9","service":"mabourse-api","timestamp":"2025-05-26 17:03:23"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships/search?page=1&limit=9 with TTL: 300s","service":"mabourse-api","timestamp":"2025-05-26 17:03:23"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships/search?page=1&limit=9","service":"mabourse-api","timestamp":"2025-06-21 23:13:52"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships/search?page=1&limit=9 with TTL: 300s","service":"mabourse-api","timestamp":"2025-06-21 23:13:53"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-06-21 23:13:57"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships with TTL: 300s","service":"mabourse-api","timestamp":"2025-06-21 23:13:57"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/health","service":"mabourse-api","timestamp":"2025-06-21 23:14:13"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/health with TTL: 60s","service":"mabourse-api","timestamp":"2025-06-21 23:14:13"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-06-21 23:25:40"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships with TTL: 300s","service":"mabourse-api","timestamp":"2025-06-21 23:25:40"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships/search?page=1&limit=9","service":"mabourse-api","timestamp":"2025-06-22 12:28:30"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships/search?page=1&limit=9 with TTL: 300s","service":"mabourse-api","timestamp":"2025-06-22 12:28:33"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9&level=Master"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships/search?page=1&limit=9&level=Master","service":"mabourse-api","timestamp":"2025-06-22 12:28:41"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9&level=Master"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships/search?page=1&limit=9&level=Master with TTL: 300s","service":"mabourse-api","timestamp":"2025-06-22 12:28:41"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9&level=Master&country=Canada"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships/search?page=1&limit=9&level=Master&country=Canada","service":"mabourse-api","timestamp":"2025-06-22 12:28:43"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9&level=Master&country=Canada"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships/search?page=1&limit=9&level=Master&country=Canada with TTL: 300s","service":"mabourse-api","timestamp":"2025-06-22 12:28:43"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships/search?page=1&limit=9","service":"mabourse-api","timestamp":"2025-06-22 12:34:10"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships/search?page=1&limit=9 with TTL: 300s","service":"mabourse-api","timestamp":"2025-06-22 12:34:10"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-06-22 12:34:31"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships with TTL: 300s","service":"mabourse-api","timestamp":"2025-06-22 12:34:31"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships/search?page=1&limit=9","service":"mabourse-api","timestamp":"2025-06-22 12:45:26"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships/search?page=1&limit=9 with TTL: 300s","service":"mabourse-api","timestamp":"2025-06-22 12:45:26"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-06-22 12:45:28"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships with TTL: 300s","service":"mabourse-api","timestamp":"2025-06-22 12:45:28"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships/search?page=1&limit=9","service":"mabourse-api","timestamp":"2025-06-22 12:57:23"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships/search?page=1&limit=9 with TTL: 300s","service":"mabourse-api","timestamp":"2025-06-22 12:57:23"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-06-22 12:59:15"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships with TTL: 300s","service":"mabourse-api","timestamp":"2025-06-22 12:59:15"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships/search?page=1&limit=9","service":"mabourse-api","timestamp":"2025-06-22 14:15:18"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships/search?page=1&limit=9 with TTL: 300s","service":"mabourse-api","timestamp":"2025-06-22 14:15:18"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-06-22 14:16:34"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships with TTL: 300s","service":"mabourse-api","timestamp":"2025-06-22 14:16:34"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-06-22 15:20:17"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships with TTL: 300s","service":"mabourse-api","timestamp":"2025-06-22 15:20:17"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships/search?page=1&limit=9","service":"mabourse-api","timestamp":"2025-06-22 16:46:02"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships/search?page=1&limit=9 with TTL: 300s","service":"mabourse-api","timestamp":"2025-06-22 16:46:02"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9&level=Licence"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships/search?page=1&limit=9&level=Licence","service":"mabourse-api","timestamp":"2025-06-22 16:46:15"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9&level=Licence"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships/search?page=1&limit=9&level=Licence with TTL: 300s","service":"mabourse-api","timestamp":"2025-06-22 16:46:15"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9&level=Licence&country=Canada"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships/search?page=1&limit=9&level=Licence&country=Canada","service":"mabourse-api","timestamp":"2025-06-22 16:46:17"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9&level=Licence&country=Canada"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships/search?page=1&limit=9&level=Licence&country=Canada with TTL: 300s","service":"mabourse-api","timestamp":"2025-06-22 16:46:17"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-06-22 16:48:24"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships with TTL: 300s","service":"mabourse-api","timestamp":"2025-06-22 16:48:24"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships/search?page=1&limit=9","service":"mabourse-api","timestamp":"2025-06-22 17:19:03"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships/search?page=1&limit=9 with TTL: 300s","service":"mabourse-api","timestamp":"2025-06-22 17:19:03"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-06-22 17:21:58"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships with TTL: 300s","service":"mabourse-api","timestamp":"2025-06-22 17:21:58"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships/search?page=1&limit=9","service":"mabourse-api","timestamp":"2025-06-22 17:25:52"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships/search?page=1&limit=9 with TTL: 300s","service":"mabourse-api","timestamp":"2025-06-22 17:25:52"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-06-22 20:41:05"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships with TTL: 300s","service":"mabourse-api","timestamp":"2025-06-22 20:41:05"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/health","service":"mabourse-api","timestamp":"2025-07-09 13:08:50"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/health with TTL: 60s","service":"mabourse-api","timestamp":"2025-07-09 13:08:50"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-09 13:08:51"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships with TTL: 300s","service":"mabourse-api","timestamp":"2025-07-09 13:08:52"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships/search?page=1&limit=9","service":"mabourse-api","timestamp":"2025-07-09 13:09:22"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships/search?page=1&limit=9 with TTL: 300s","service":"mabourse-api","timestamp":"2025-07-09 13:09:22"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/health","service":"mabourse-api","timestamp":"2025-07-09 13:09:51"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/health with TTL: 60s","service":"mabourse-api","timestamp":"2025-07-09 13:09:51"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/health","service":"mabourse-api","timestamp":"2025-07-09 13:11:13"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/health with TTL: 60s","service":"mabourse-api","timestamp":"2025-07-09 13:11:13"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9&level=Licence"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships/search?page=1&limit=9&level=Licence","service":"mabourse-api","timestamp":"2025-07-09 13:13:18"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9&level=Licence"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships/search?page=1&limit=9&level=Licence with TTL: 300s","service":"mabourse-api","timestamp":"2025-07-09 13:13:18"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9&level=Licence&country=France"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships/search?page=1&limit=9&level=Licence&country=France","service":"mabourse-api","timestamp":"2025-07-09 13:13:23"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9&level=Licence&country=France"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships/search?page=1&limit=9&level=Licence&country=France with TTL: 300s","service":"mabourse-api","timestamp":"2025-07-09 13:13:23"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-09 13:13:56"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships with TTL: 300s","service":"mabourse-api","timestamp":"2025-07-09 13:13:56"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/health","service":"mabourse-api","timestamp":"2025-07-09 13:14:02"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/health with TTL: 60s","service":"mabourse-api","timestamp":"2025-07-09 13:14:02"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/health","service":"mabourse-api","timestamp":"2025-07-09 13:15:06"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/health with TTL: 60s","service":"mabourse-api","timestamp":"2025-07-09 13:15:06"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/health","service":"mabourse-api","timestamp":"2025-07-11 15:47:56"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/health with TTL: 60s","service":"mabourse-api","timestamp":"2025-07-11 15:47:56"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-11 15:47:57"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships with TTL: 300s","service":"mabourse-api","timestamp":"2025-07-11 15:47:59"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/health","service":"mabourse-api","timestamp":"2025-07-11 15:52:54"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/health with TTL: 60s","service":"mabourse-api","timestamp":"2025-07-11 15:52:54"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships/search?page=1&limit=9","service":"mabourse-api","timestamp":"2025-07-11 15:53:01"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships/search?page=1&limit=9 with TTL: 300s","service":"mabourse-api","timestamp":"2025-07-11 15:53:01"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/health","service":"mabourse-api","timestamp":"2025-07-11 16:08:26"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/health with TTL: 60s","service":"mabourse-api","timestamp":"2025-07-11 16:08:26"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/health","service":"mabourse-api","timestamp":"2025-07-11 19:31:41"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/health with TTL: 60s","service":"mabourse-api","timestamp":"2025-07-11 19:31:41"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-11 19:35:16"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships with TTL: 300s","service":"mabourse-api","timestamp":"2025-07-11 19:35:16"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships?page=1&limit=5"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships?page=1&limit=5","service":"mabourse-api","timestamp":"2025-07-11 22:37:48"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships?page=1&limit=5"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships?page=1&limit=5 with TTL: 300s","service":"mabourse-api","timestamp":"2025-07-11 22:37:48"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/health","service":"mabourse-api","timestamp":"2025-07-11 22:38:56"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/health with TTL: 60s","service":"mabourse-api","timestamp":"2025-07-11 22:38:56"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-11 22:38:56"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships with TTL: 300s","service":"mabourse-api","timestamp":"2025-07-11 22:38:56"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-11 22:38:56"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-11 22:38:56"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-11 22:38:56"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/health","service":"mabourse-api","timestamp":"2025-07-11 22:39:48"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-11 22:39:48"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-11 22:39:48"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-11 22:39:48"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-11 22:39:48"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/health","service":"mabourse-api","timestamp":"2025-07-11 22:42:28"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/health with TTL: 60s","service":"mabourse-api","timestamp":"2025-07-11 22:42:28"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-11 22:42:39"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships with TTL: 300s","service":"mabourse-api","timestamp":"2025-07-11 22:42:39"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships/search?page=1&limit=9","service":"mabourse-api","timestamp":"2025-07-11 22:44:35"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships/search?page=1&limit=9","service":"mabourse-api","timestamp":"2025-07-11 22:44:37"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships/search?page=1&limit=9 with TTL: 300s","service":"mabourse-api","timestamp":"2025-07-11 22:44:39"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-11 22:44:41"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-11 22:44:41"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-11 22:44:41"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-11 22:44:41"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/scholarships/search?page=1&limit=9","service":"mabourse-api","timestamp":"2025-07-11 22:44:46"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/scholarships/search?page=1&limit=9","service":"mabourse-api","timestamp":"2025-07-11 22:44:46"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-11 22:44:47"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-11 22:44:47"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-11 22:44:47"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-11 22:44:47"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/scholarships/search?page=1&limit=9","service":"mabourse-api","timestamp":"2025-07-11 22:45:03"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships/search?page=1&limit=9"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/scholarships/search?page=1&limit=9","service":"mabourse-api","timestamp":"2025-07-11 22:45:03"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/health","service":"mabourse-api","timestamp":"2025-07-11 22:45:16"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/health with TTL: 60s","service":"mabourse-api","timestamp":"2025-07-11 22:45:16"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/health","service":"mabourse-api","timestamp":"2025-07-11 22:45:18"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/health","service":"mabourse-api","timestamp":"2025-07-11 22:45:20"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/health","service":"mabourse-api","timestamp":"2025-07-11 22:59:59"}
{"category":"cache","data":{"method":"GET","url":"/api/health"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/health with TTL: 60s","service":"mabourse-api","timestamp":"2025-07-11 22:59:59"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-11 23:07:44"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships with TTL: 300s","service":"mabourse-api","timestamp":"2025-07-11 23:07:44"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:MISS] Cache miss for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-11 23:11:56"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:SET] Cache set for key: GET:/api/scholarships with TTL: 300s","service":"mabourse-api","timestamp":"2025-07-11 23:11:56"}
{"category":"cache","data":{"method":"GET","url":"/api/scholarships"},"level":"debug","message":"[CACHE:HIT] Cache hit for key: GET:/api/scholarships","service":"mabourse-api","timestamp":"2025-07-11 23:13:07"}
