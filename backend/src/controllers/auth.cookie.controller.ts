import { Request, Response } from 'express';
import { validationResult } from 'express-validator';
import jwt, { SignOptions } from 'jsonwebtoken';
import bcrypt from 'bcryptjs';
import { User } from '../models/User';
import { sendEmail } from '../utils/email';
import crypto from 'crypto';
import { rateLimit } from 'express-rate-limit';
import {
  sendSuccess,
  sendError,
  sendNotFound,
  sendValidationError,
  sendUnauthorized
} from '../utils/apiResponse';
import dateUtils from '../utils/dateUtils';
import authLogger, {
  AuthEventType,
  SecurityLevel,
  logLoginSuccess,
  logLoginFailure,
  logAccountLocked,
  logSuspiciousActivity
} from '../utils/authLogger';

// Rate limiting for login attempts
export const loginLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 5, // 5 attempts
  message: {
    success: false,
    message: 'Too many login attempts',
    error: 'Please try again after 15 minutes'
  },
});

// Rate limiting for password reset
export const resetPasswordLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 3, // 3 attempts
  message: {
    success: false,
    message: 'Too many password reset attempts',
    error: 'Please try again after 1 hour'
  },
});

/**
 * Register a new user
 * @param req Request
 * @param res Response
 */
export const register = async (req: Request, res: Response) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return sendValidationError(res, errors.array());
    }

    const { name, email, password } = req.body;

    // Check if user already exists
    const existingUser = await User.findByEmail(email);

    if (existingUser) {
      return sendError(res, 'User already exists', null, 409);
    }

    // Create new user
    const user = await User.create({
      name,
      email,
      password, // User.create will hash the password
      role: 'user',
      failedLoginAttempts: 0,
      mustChangePassword: false
    });

    // Generate token
    const jwtSecret = process.env.JWT_SECRET || 'your-secret-key';
    const jwtOptions: SignOptions = { expiresIn: '1d' }; // Fixed expiration time
    const token = jwt.sign(
      { id: user.id, role: user.role },
      jwtSecret,
      jwtOptions
    );

    // Set token in HTTP-only cookie
    res.cookie('token', token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax', // Use 'none' in production with secure=true, 'lax' in development
      maxAge: 24 * 60 * 60 * 1000, // 1 day
      path: '/'
    });

    return sendSuccess(
      res,
      {
        user: {
          id: user.id,
          name: user.name,
          email: user.email,
          role: user.role
        }
      },
      'Registration successful',
      201
    );
  } catch (error) {
    console.error('Registration error:', error);
    return sendError(res, 'Registration failed', error);
  }
};

/**
 * Login a user
 * @param req Request
 * @param res Response
 */
export const login = async (req: Request, res: Response) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return sendValidationError(res, errors.array());
    }

    const { email, password } = req.body;

    // Find user
    const user = await User.findByEmail(email);

    if (!user) {
      return sendUnauthorized(res, 'Invalid credentials');
    }

    // Check if account is locked
    if (user.lockUntil && user.lockUntil > new Date()) {
      // Log account locked event
      logAccountLocked(user.id!, user.email, req.ip || 'unknown');

      return sendError(
        res,
        'Account is locked',
        'Please try again later or reset your password',
        423 // Locked
      );
    }

    // Check password
    const isMatch = await bcrypt.compare(password, user.password!);
    if (!isMatch) {
      // Increment failed login attempts
      const newFailedAttempts = user.failedLoginAttempts + 1;
      const shouldLock = newFailedAttempts >= 5;
      const lockUntil = shouldLock ? new Date(Date.now() + 30 * 60 * 1000) : undefined; // Lock for 30 minutes after 5 failed attempts

      await User.update(user.id!, {
        failedLoginAttempts: newFailedAttempts,
        lockUntil
      });

      // Log login failure
      logLoginFailure(
        user.email,
        'Invalid password',
        req.ip || 'unknown',
        newFailedAttempts,
        req.headers['user-agent']
      );

      // If account is now locked, log that too
      if (shouldLock) {
        logAccountLocked(user.id!, user.email, req.ip || 'unknown');
      }

      return sendUnauthorized(res, 'Invalid credentials');
    }

    // Reset failed login attempts and update last login
    await User.update(user.id!, {
      failedLoginAttempts: 0,
      lockUntil: undefined,
      lastLogin: new Date()
    });

    // Generate token
    const jwtSecret = process.env.JWT_SECRET || 'your-secret-key';
    const jwtOptions: SignOptions = { expiresIn: '1d' }; // Fixed expiration time
    const token = jwt.sign(
      { id: user.id, role: user.role },
      jwtSecret,
      jwtOptions
    );

    // Set token in HTTP-only cookie
    res.cookie('token', token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax', // Use 'none' in production with secure=true, 'lax' in development
      maxAge: 24 * 60 * 60 * 1000, // 1 day
      path: '/'
    });

    // Log successful login
    logLoginSuccess(
      user.id!,
      user.email,
      user.role,
      req.ip || 'unknown',
      req.headers['user-agent']
    );

    return sendSuccess(
      res,
      {
        user: {
          id: user.id,
          name: user.name,
          email: user.email,
          role: user.role
        }
      },
      'Login successful'
    );
  } catch (error) {
    console.error('Login error:', error);
    return sendError(res, 'Login failed', error);
  }
};

/**
 * Logout a user
 * @param req Request
 * @param res Response
 */
export const logout = async (req: Request, res: Response) => {
  try {
    // Get user info before clearing cookie
    const userId = req.user?.id;
    const userEmail = req.user?.email;
    const userRole = req.user?.role;

    // Clear the token cookie
    res.clearCookie('token', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax', // Use 'none' in production with secure=true, 'lax' in development
      path: '/'
    });

    // Log logout event if we have user info
    if (userId && userEmail) {
      authLogger.logAuthEvent(
        AuthEventType.LOGOUT,
        `User logged out: ${userEmail}`,
        { userId, email: userEmail, role: userRole, ip: req.ip },
        SecurityLevel.INFO
      );
    }

    return sendSuccess(res, null, 'Logout successful');
  } catch (error) {
    console.error('Logout error:', error);
    return sendError(res, 'Logout failed', error);
  }
};

/**
 * Get the current user's profile
 * @param req Request
 * @param res Response
 */
export const getProfile = async (req: Request, res: Response) => {
  try {
    const userId = req.user?.id;

    if (!userId) {
      return sendUnauthorized(res, 'User not authenticated');
    }

    const user = await User.findById(userId);

    if (!user) {
      return sendNotFound(res, 'User not found');
    }

    return sendSuccess(res, { user }, 'Profile retrieved successfully');
  } catch (error) {
    console.error('Get profile error:', error);
    return sendError(res, 'Failed to retrieve profile', error);
  }
};
