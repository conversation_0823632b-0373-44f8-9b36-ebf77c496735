import express from 'express';
import { body } from 'express-validator';
import { auth } from '../middleware/auth.unified';
import {
  initializeTwoFactor,
  verifyAndEnableTwoFactor,
  verifyTwoFactorToken,
  disableTwoFactor,
  getTwoFactorStatus
} from '../controllers/twoFactor.controller';

const router = express.Router();

// Public route for verifying 2FA token during login
router.post(
  '/verify',
  [
    body('adminId').isNumeric().withMessage('Admin ID is required'),
    body('token').notEmpty().withMessage('Token is required'),
    body('isBackupCode').optional().isBoolean()
  ],
  verifyTwoFactorToken
);

// Protected routes (require authentication)
router.use(auth);

// Get 2FA status
router.get('/status', getTwoFactorStatus);

// Initialize 2FA setup
router.post('/initialize', initializeTwoFactor);

// Verify and enable 2FA
router.post(
  '/enable',
  [
    body('token').notEmpty().withMessage('Token is required')
  ],
  verifyAndEnableTwoFactor
);

// Disable 2FA
router.post(
  '/disable',
  [
    body('token').notEmpty().withMessage('Token is required')
  ],
  disableTwoFactor
);

export default router;
