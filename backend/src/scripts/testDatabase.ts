#!/usr/bin/env ts-node

/**
 * Test Database Connection Script
 * 
 * This script tests the PostgreSQL database connection
 */

import dotenv from 'dotenv';
import path from 'path';
import { initializeDatabase, testConnection, closeDatabase } from '../config/database';

// Load environment variables
dotenv.config({ path: path.join(__dirname, '../../.env.development') });

async function main() {
  try {
    console.log('Testing database connection...');
    console.log('Database config:', {
      host: process.env.DB_HOST,
      port: process.env.DB_PORT,
      database: process.env.DB_NAME,
      user: process.env.DB_USER
    });

    // Initialize database connection
    initializeDatabase();
    
    // Test connection
    const isConnected = await testConnection();
    
    if (isConnected) {
      console.log('✅ Database connection successful!');
    } else {
      console.log('❌ Database connection failed!');
    }
    
    // Close connection
    await closeDatabase();
    
    process.exit(isConnected ? 0 : 1);
  } catch (error) {
    console.error('Database test failed:', error);
    process.exit(1);
  }
}

// Run the test
main();
