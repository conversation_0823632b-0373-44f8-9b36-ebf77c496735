#!/usr/bin/env ts-node

/**
 * Run Database Migration Script
 * 
 * This script runs the database migration to create schema and initial data
 */

import { runMigrations } from '../database/migrate';

async function main() {
  try {
    console.log('Starting database migration...');
    await runMigrations();
    console.log('Migration completed successfully!');
    process.exit(0);
  } catch (error) {
    console.error('Migration failed:', error);
    process.exit(1);
  }
}

// Run the migration
main();
