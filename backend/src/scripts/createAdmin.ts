import bcrypt from 'bcryptjs';
import { Admin } from '../models/Admin';
import { initializeDatabase, closeDatabase } from '../config/database';

async function createAdmin() {
  try {
    // Initialize database connection
    await initializeDatabase();

    const email = '<EMAIL>';
    const password = 'admin123'; // You should change this in production

    console.log('Creating admin user...');

    // Check if admin already exists
    const existingAdmin = await Admin.findByEmail(email);

    if (existingAdmin) {
      console.log('Admin user already exists:', {
        id: existingAdmin.id,
        email: existingAdmin.email,
        name: existingAdmin.name,
        role: existingAdmin.role,
        isMainAdmin: existingAdmin.isMainAdmin
      });
      return;
    }

    // Hash password
    const hashedPassword = await bcrypt.hash(password, 10);

    // Create admin user
    const adminData = {
      email,
      password: hashedPassword,
      name: 'Main Admin',
      role: 'admin',
      privileges: ['all'],
      isMainAdmin: true,
      twoFactorEnabled: false,
      failedLoginAttempts: 0
    };

    const admin = await Admin.create(adminData);

    console.log('Admin user created successfully:', {
      id: admin.id,
      email: admin.email,
      name: admin.name,
      role: admin.role,
      isMainAdmin: admin.isMainAdmin
    });

    console.log('\nYou can now login with:');
    console.log('Email:', email);
    console.log('Password:', password);
    console.log('\nPlease change the password after first login!');

  } catch (error) {
    console.error('Error creating admin user:', error);
  } finally {
    // Close database connection
    await closeDatabase();
  }
}

createAdmin();