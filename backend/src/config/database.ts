/**
 * PostgreSQL Database Configuration and Connection Module
 * 
 * This module provides a clean interface for PostgreSQL database operations
 * with connection pooling and proper error handling.
 */

import { Pool, PoolClient, QueryResult, QueryResultRow } from 'pg';
import { getEnv, getNumEnv, getBoolEnv } from '../utils/envValidator';

// Database configuration interface
interface DatabaseConfig {
  host: string;
  port: number;
  database: string;
  user: string;
  password: string;
  ssl?: boolean;
  max?: number;
  idleTimeoutMillis?: number;
  connectionTimeoutMillis?: number;
}

// Database connection pool
let pool: Pool | null = null;

/**
 * Get database configuration from environment variables
 */
function getDatabaseConfig(): DatabaseConfig {
  return {
    host: getEnv('DB_HOST', 'localhost'),
    port: getNumEnv('DB_PORT', 5432),
    database: getEnv('DB_NAME', 'mabourse'),
    user: getEnv('DB_USER', 'postgres'),
    password: getEnv('DB_PASSWORD', 'postgres'),
    ssl: getBoolEnv('DB_SSL', false),
    max: getNumEnv('DB_POOL_MAX', 20), // Maximum number of clients in the pool
    idleTimeoutMillis: getNumEnv('DB_IDLE_TIMEOUT', 30000), // Close idle clients after 30 seconds
    connectionTimeoutMillis: getNumEnv('DB_CONNECTION_TIMEOUT', 2000), // Return an error after 2 seconds if connection could not be established
  };
}

/**
 * Initialize database connection pool
 */
export function initializeDatabase(): Pool {
  if (pool) {
    return pool;
  }

  const config = getDatabaseConfig();
  
  pool = new Pool(config);

  // Handle pool errors
  pool.on('error', (err) => {
    console.error('Unexpected error on idle client', err);
    process.exit(-1);
  });

  // Handle pool connection events
  pool.on('connect', (client) => {
    console.log('New client connected to database');
  });

  pool.on('remove', (client) => {
    console.log('Client removed from pool');
  });

  console.log(`Database pool initialized with max ${config.max} connections`);
  return pool;
}

/**
 * Get database connection pool
 */
export function getDatabase(): Pool {
  if (!pool) {
    throw new Error('Database not initialized. Call initializeDatabase() first.');
  }
  return pool;
}

/**
 * Execute a query with parameters
 */
export async function query<T extends QueryResultRow = any>(text: string, params?: any[]): Promise<QueryResult<T>> {
  const db = getDatabase();
  const start = Date.now();
  
  try {
    const result = await db.query<T>(text, params);
    const duration = Date.now() - start;
    
    // Log slow queries (over 100ms)
    if (duration > 100) {
      console.warn(`Slow query detected (${duration}ms):`, text.substring(0, 100));
    }
    
    return result;
  } catch (error) {
    console.error('Database query error:', error);
    console.error('Query:', text);
    console.error('Params:', params);
    throw error;
  }
}

/**
 * Execute a query with a specific client (for transactions)
 */
export async function queryWithClient<T extends QueryResultRow = any>(
  client: PoolClient,
  text: string,
  params?: any[]
): Promise<QueryResult<T>> {
  const start = Date.now();
  
  try {
    const result = await client.query<T>(text, params);
    const duration = Date.now() - start;
    
    // Log slow queries (over 100ms)
    if (duration > 100) {
      console.warn(`Slow query detected (${duration}ms):`, text.substring(0, 100));
    }
    
    return result;
  } catch (error) {
    console.error('Database query error:', error);
    console.error('Query:', text);
    console.error('Params:', params);
    throw error;
  }
}

/**
 * Execute multiple queries in a transaction
 */
export async function transaction<T>(
  callback: (client: PoolClient) => Promise<T>
): Promise<T> {
  const db = getDatabase();
  const client = await db.connect();
  
  try {
    await client.query('BEGIN');
    const result = await callback(client);
    await client.query('COMMIT');
    return result;
  } catch (error) {
    await client.query('ROLLBACK');
    throw error;
  } finally {
    client.release();
  }
}

/**
 * Test database connection
 */
export async function testConnection(): Promise<boolean> {
  try {
    const result = await query('SELECT NOW() as current_time');
    console.log('Database connection test successful:', result.rows[0].current_time);
    return true;
  } catch (error) {
    console.error('Database connection test failed:', error);
    return false;
  }
}

/**
 * Close database connection pool
 */
export async function closeDatabase(): Promise<void> {
  if (pool) {
    await pool.end();
    pool = null;
    console.log('Database connection pool closed');
  }
}

/**
 * Get database statistics
 */
export async function getDatabaseStats(): Promise<{
  totalConnections: number;
  idleConnections: number;
  waitingCount: number;
}> {
  const db = getDatabase();
  return {
    totalConnections: db.totalCount,
    idleConnections: db.idleCount,
    waitingCount: db.waitingCount,
  };
}
