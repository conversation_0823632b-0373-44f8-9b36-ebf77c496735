"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.mainAdminAuth = exports.adminAuth = exports.auth = void 0;
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const Admin_1 = require("../models/Admin");
const User_1 = require("../models/User");
const auth = async (req, res, next) => {
    var _a;
    try {
        const token = (_a = req.header('Authorization')) === null || _a === void 0 ? void 0 : _a.replace('Bearer ', '');
        if (!token) {
            return res.status(401).json({ message: 'No token, authorization denied' });
        }
        const decoded = jsonwebtoken_1.default.verify(token, process.env.JWT_SECRET || 'your-secret-key');
        // Check if the token belongs to an admin
        if (decoded.role === 'admin' || decoded.isMainAdmin) {
            const admin = await Admin_1.Admin.findById(decoded.id);
            if (!admin) {
                return res.status(401).json({ message: 'Admin not found' });
            }
            req.user = {
                id: decoded.id,
                email: decoded.email || admin.email,
                role: decoded.role,
                isMainAdmin: decoded.isMainAdmin || false
            };
        }
        else {
            // Check if the token belongs to a regular user
            const user = await User_1.User.findById(decoded.id);
            if (!user) {
                return res.status(401).json({ message: 'User not found' });
            }
            req.user = {
                id: decoded.id,
                email: decoded.email || user.email,
                role: decoded.role,
                isMainAdmin: false
            };
        }
        next();
    }
    catch (error) {
        res.status(401).json({ message: 'Token is not valid' });
    }
};
exports.auth = auth;
const adminAuth = async (req, res, next) => {
    try {
        await (0, exports.auth)(req, res, () => {
            // Check if the user is an admin (either regular admin or main admin)
            if (!req.user || (req.user.role !== 'admin' && !req.user.isMainAdmin)) {
                console.log('Access denied for non-admin user:', req.user);
                return res.status(403).json({ message: 'Access denied. Admin privileges required.' });
            }
            next();
        });
    }
    catch (error) {
        console.error('Authentication error:', error);
        res.status(401).json({ message: 'Please authenticate' });
    }
};
exports.adminAuth = adminAuth;
// Middleware to check if user is a main admin
const mainAdminAuth = async (req, res, next) => {
    try {
        await (0, exports.auth)(req, res, () => {
            // Check if the user is a main admin
            if (!req.user || !req.user.isMainAdmin) {
                console.log('Access denied for non-main admin:', req.user);
                return res.status(403).json({ message: 'Access denied. Main admin privileges required.' });
            }
            next();
        });
    }
    catch (error) {
        console.error('Authentication error:', error);
        res.status(401).json({ message: 'Please authenticate' });
    }
};
exports.mainAdminAuth = mainAdminAuth;
