"use strict";
/**
 * Enhanced Input Validation and Sanitization Middleware
 *
 * This middleware provides comprehensive input validation and sanitization
 * to protect against common security vulnerabilities.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.securityHeaders = exports.sanitizeUrlParams = exports.validateAndSanitize = exports.validate = exports.sanitizeRequestBody = void 0;
const express_validator_1 = require("express-validator");
const apiResponse_1 = require("../utils/apiResponse");
const logger_1 = require("../utils/logger");
// import xss from 'xss'; // Temporarily disabled
const xss = (str) => str; // Temporary placeholder
/**
 * Sanitize request body to prevent XSS attacks
 * @param req Express request
 */
const sanitizeRequestBody = (req, res, next) => {
    if (req.body) {
        // Create a sanitized copy of the request body
        const sanitizedBody = {};
        // Recursively sanitize object properties
        const sanitizeObject = (obj, target) => {
            for (const key in obj) {
                if (Object.prototype.hasOwnProperty.call(obj, key)) {
                    const value = obj[key];
                    // Skip sanitization for specific fields
                    const skipSanitization = [
                        'password',
                        'confirmPassword',
                        'token',
                        'resetPasswordToken',
                        'twoFactorSecret',
                        'twoFactorBackupCodes',
                    ];
                    if (skipSanitization.includes(key)) {
                        target[key] = value;
                        continue;
                    }
                    // Recursively sanitize nested objects
                    if (value && typeof value === 'object' && !Array.isArray(value)) {
                        target[key] = {};
                        sanitizeObject(value, target[key]);
                    }
                    // Sanitize arrays
                    else if (Array.isArray(value)) {
                        target[key] = value.map(item => {
                            if (typeof item === 'string') {
                                return xss(item);
                            }
                            else if (item && typeof item === 'object') {
                                const sanitizedItem = {};
                                sanitizeObject(item, sanitizedItem);
                                return sanitizedItem;
                            }
                            return item;
                        });
                    }
                    // Sanitize strings
                    else if (typeof value === 'string') {
                        target[key] = xss(value);
                    }
                    // Keep other types as is
                    else {
                        target[key] = value;
                    }
                }
            }
        };
        // Perform sanitization
        sanitizeObject(req.body, sanitizedBody);
        // Replace the original body with the sanitized version
        req.body = sanitizedBody;
    }
    next();
};
exports.sanitizeRequestBody = sanitizeRequestBody;
/**
 * Validate request parameters
 * @param validations Array of validation chains
 */
const validate = (validations) => {
    return async (req, res, next) => {
        // Execute all validations
        await Promise.all(validations.map(validation => validation.run(req)));
        // Check for validation errors
        const errors = (0, express_validator_1.validationResult)(req);
        if (errors.isEmpty()) {
            return next();
        }
        // Log validation errors
        logger_1.apiLogger.warn('Validation errors', {
            path: req.originalUrl,
            method: req.method,
            errors: errors.array()
        });
        // Return validation errors
        return (0, apiResponse_1.sendValidationError)(res, errors.array());
    };
};
exports.validate = validate;
/**
 * Validate and sanitize request parameters
 * @param validations Array of validation chains
 */
const validateAndSanitize = (validations) => {
    return [
        exports.sanitizeRequestBody,
        (0, exports.validate)(validations)
    ];
};
exports.validateAndSanitize = validateAndSanitize;
/**
 * Sanitize URL parameters to prevent XSS attacks
 */
const sanitizeUrlParams = (req, res, next) => {
    // Sanitize query parameters
    if (req.query) {
        for (const key in req.query) {
            if (Object.prototype.hasOwnProperty.call(req.query, key)) {
                const value = req.query[key];
                if (typeof value === 'string') {
                    req.query[key] = xss(value);
                }
            }
        }
    }
    // Sanitize URL parameters
    if (req.params) {
        for (const key in req.params) {
            if (Object.prototype.hasOwnProperty.call(req.params, key)) {
                const value = req.params[key];
                if (typeof value === 'string') {
                    req.params[key] = xss(value);
                }
            }
        }
    }
    next();
};
exports.sanitizeUrlParams = sanitizeUrlParams;
/**
 * Middleware to prevent common security vulnerabilities
 */
const securityHeaders = (req, res, next) => {
    // Set security headers
    res.set('X-Content-Type-Options', 'nosniff');
    res.set('X-Frame-Options', 'DENY');
    res.set('X-XSS-Protection', '1; mode=block');
    res.set('Strict-Transport-Security', 'max-age=31536000; includeSubDomains');
    res.set('Content-Security-Policy', "default-src 'self'; img-src 'self' data:; style-src 'self' 'unsafe-inline'; script-src 'self'");
    res.set('Referrer-Policy', 'strict-origin-when-cross-origin');
    next();
};
exports.securityHeaders = securityHeaders;
exports.default = {
    sanitizeRequestBody: exports.sanitizeRequestBody,
    validate: exports.validate,
    validateAndSanitize: exports.validateAndSanitize,
    sanitizeUrlParams: exports.sanitizeUrlParams,
    securityHeaders: exports.securityHeaders,
};
