"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.asyncHandler = exports.notFoundHandler = exports.errorHandler = exports.AppError = exports.ErrorType = void 0;
// Removed Prisma import as we're using PostgreSQL directly
const logger_1 = __importDefault(require("../utils/logger"));
// Define error types for better classification
var ErrorType;
(function (ErrorType) {
    ErrorType["VALIDATION"] = "VALIDATION_ERROR";
    ErrorType["AUTHENTICATION"] = "AUTHENTICATION_ERROR";
    ErrorType["AUTHORIZATION"] = "AUTHORIZATION_ERROR";
    ErrorType["NOT_FOUND"] = "NOT_FOUND_ERROR";
    ErrorType["DATABASE"] = "DATABASE_ERROR";
    ErrorType["INTERNAL"] = "INTERNAL_ERROR";
    ErrorType["EXTERNAL_SERVICE"] = "EXTERNAL_SERVICE_ERROR";
})(ErrorType || (exports.ErrorType = ErrorType = {}));
// Custom error class with type
class AppError extends Error {
    constructor(message, type, statusCode, details) {
        super(message);
        this.type = type;
        this.statusCode = statusCode;
        this.details = details;
        this.name = 'AppError';
    }
}
exports.AppError = AppError;
// Middleware to handle errors
const errorHandler = (err, req, res, next) => {
    // Default error values
    let statusCode = 500;
    let errorType = ErrorType.INTERNAL;
    let message = 'Internal server error';
    let details = undefined;
    // Handle AppError instances
    if (err instanceof AppError) {
        statusCode = err.statusCode;
        errorType = err.type;
        message = err.message;
        details = err.details;
    }
    // Handle PostgreSQL errors
    else if (err.code && typeof err.code === 'string') {
        errorType = ErrorType.DATABASE;
        // Handle specific PostgreSQL error codes
        switch (err.code) {
            case '23505': // unique_violation
                statusCode = 409;
                message = 'A record with this data already exists';
                break;
            case '23503': // foreign_key_violation
                statusCode = 400;
                message = 'Referenced record does not exist';
                break;
            case '23502': // not_null_violation
                statusCode = 400;
                message = 'Required field is missing';
                break;
            default:
                message = 'Database error';
        }
    }
    // Handle validation errors from express-validator
    else if (err.name === 'ValidationError' || err.errors) {
        statusCode = 400;
        errorType = ErrorType.VALIDATION;
        message = 'Validation error';
        details = err.errors || err.message;
    }
    // Handle JWT errors
    else if (err.name === 'JsonWebTokenError' || err.name === 'TokenExpiredError') {
        statusCode = 401;
        errorType = ErrorType.AUTHENTICATION;
        message = err.name === 'TokenExpiredError' ? 'Token expired' : 'Invalid token';
    }
    // Log the error
    if (statusCode >= 500) {
        logger_1.default.error(`[${errorType}] ${message}`, {
            error: {
                name: err.name,
                message: err.message,
                stack: err.stack,
                details,
            },
            request: {
                method: req.method,
                url: req.originalUrl,
                body: req.body,
                params: req.params,
                query: req.query,
            },
        });
    }
    else {
        logger_1.default.warn(`[${errorType}] ${message}`, {
            error: {
                name: err.name,
                message: err.message,
                details,
            },
            request: {
                method: req.method,
                url: req.originalUrl,
            },
        });
    }
    // Send response
    res.status(statusCode).json({
        error: {
            type: errorType,
            message,
            ...(details && { details }),
            ...(process.env.NODE_ENV !== 'production' && { stack: err.stack }),
        },
    });
};
exports.errorHandler = errorHandler;
// Middleware to handle 404 errors
const notFoundHandler = (req, res, next) => {
    const error = new AppError(`Not Found - ${req.originalUrl}`, ErrorType.NOT_FOUND, 404);
    next(error);
};
exports.notFoundHandler = notFoundHandler;
// Utility function to wrap async route handlers
const asyncHandler = (fn) => (req, res, next) => {
    Promise.resolve(fn(req, res, next)).catch(next);
};
exports.asyncHandler = asyncHandler;
