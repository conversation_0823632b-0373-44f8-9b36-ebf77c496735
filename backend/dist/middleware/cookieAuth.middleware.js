"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.mainAdminCookieAuth = exports.adminCookieAuth = exports.cookieAuth = void 0;
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const Admin_1 = require("../models/Admin");
const User_1 = require("../models/User");
/**
 * Authentication middleware using HTTP-only cookies
 * This is a more secure approach than using localStorage for tokens
 */
const cookieAuth = async (req, res, next) => {
    try {
        // Get token from cookies instead of Authorization header
        const token = req.cookies.token;
        if (!token) {
            return res.status(401).json({
                success: false,
                message: 'Authentication required',
                error: 'No authentication token found'
            });
        }
        // Verify the token
        const decoded = jsonwebtoken_1.default.verify(token, process.env.JWT_SECRET || 'your-secret-key');
        // Check if the token belongs to an admin
        if (decoded.role === 'admin' || decoded.role === 'super_admin' || decoded.isMainAdmin) {
            const admin = await Admin_1.Admin.findById(decoded.id);
            if (!admin) {
                return res.status(401).json({
                    success: false,
                    message: 'Authentication failed',
                    error: 'Admin account not found'
                });
            }
            // Attach admin info to request
            req.user = {
                id: decoded.id,
                email: decoded.email || admin.email,
                role: decoded.role || admin.role,
                isMainAdmin: decoded.isMainAdmin || admin.isMainAdmin || false
            };
        }
        else {
            // Check if the token belongs to a regular user
            const user = await User_1.User.findById(decoded.id);
            if (!user) {
                return res.status(401).json({
                    success: false,
                    message: 'Authentication failed',
                    error: 'User account not found'
                });
            }
            // Attach user info to request
            req.user = {
                id: decoded.id,
                email: decoded.email || user.email,
                role: decoded.role || user.role || 'user',
                isMainAdmin: false
            };
        }
        // Check if token is about to expire and refresh if needed
        const tokenExp = decoded.exp;
        const currentTime = Math.floor(Date.now() / 1000);
        // If token will expire in less than 15 minutes (900 seconds), refresh it
        if (tokenExp && tokenExp - currentTime < 900) {
            const jwtSecret = process.env.JWT_SECRET || 'your-secret-key';
            const jwtOptions = { expiresIn: '1d' }; // Fixed expiration time
            const newToken = jsonwebtoken_1.default.sign({
                id: req.user.id,
                email: req.user.email,
                role: req.user.role,
                isMainAdmin: req.user.isMainAdmin
            }, jwtSecret, jwtOptions);
            // Set the new token in a cookie
            res.cookie('token', newToken, {
                httpOnly: true,
                secure: process.env.NODE_ENV === 'production',
                sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax', // Use 'none' in production with secure=true, 'lax' in development
                maxAge: 24 * 60 * 60 * 1000, // 1 day
                path: '/'
            });
        }
        next();
    }
    catch (error) {
        if (error instanceof jsonwebtoken_1.default.JsonWebTokenError) {
            // Clear the invalid token
            res.clearCookie('token');
            return res.status(401).json({
                success: false,
                message: 'Authentication failed',
                error: 'Invalid or expired token'
            });
        }
        console.error('Authentication error:', error);
        return res.status(500).json({
            success: false,
            message: 'Authentication error',
            error: process.env.NODE_ENV === 'development' ? error.message : undefined
        });
    }
};
exports.cookieAuth = cookieAuth;
/**
 * Admin authentication middleware
 * Requires the user to be an admin
 */
const adminCookieAuth = async (req, res, next) => {
    try {
        await (0, exports.cookieAuth)(req, res, () => {
            // Check if the user is an admin (either regular admin or main admin)
            if (!req.user || (req.user.role !== 'admin' && req.user.role !== 'super_admin' && !req.user.isMainAdmin)) {
                return res.status(403).json({
                    success: false,
                    message: 'Access denied',
                    error: 'Admin privileges required'
                });
            }
            next();
        });
    }
    catch (error) {
        console.error('Admin authentication error:', error);
        res.status(401).json({
            success: false,
            message: 'Authentication required',
            error: 'Please authenticate as an admin'
        });
    }
};
exports.adminCookieAuth = adminCookieAuth;
/**
 * Main admin authentication middleware
 * Requires the user to be a main admin
 */
const mainAdminCookieAuth = async (req, res, next) => {
    try {
        await (0, exports.cookieAuth)(req, res, () => {
            // Check if the user is a main admin
            if (!req.user || !req.user.isMainAdmin) {
                return res.status(403).json({
                    success: false,
                    message: 'Access denied',
                    error: 'Main admin privileges required'
                });
            }
            next();
        });
    }
    catch (error) {
        console.error('Main admin authentication error:', error);
        res.status(401).json({
            success: false,
            message: 'Authentication required',
            error: 'Please authenticate as a main admin'
        });
    }
};
exports.mainAdminCookieAuth = mainAdminCookieAuth;
exports.default = {
    cookieAuth: exports.cookieAuth,
    adminCookieAuth: exports.adminCookieAuth,
    mainAdminCookieAuth: exports.mainAdminCookieAuth
};
