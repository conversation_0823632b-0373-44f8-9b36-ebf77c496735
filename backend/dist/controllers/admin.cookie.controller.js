"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getAdmins = exports.getCurrentAdmin = exports.logout = exports.login = exports.loginLimiter = void 0;
const express_validator_1 = require("express-validator");
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const bcryptjs_1 = __importDefault(require("bcryptjs"));
const Admin_1 = require("../models/Admin");
const express_rate_limit_1 = require("express-rate-limit");
const apiResponse_1 = require("../utils/apiResponse");
// Using PostgreSQL models directly instead of Prisma
// Rate limiting for login attempts
exports.loginLimiter = (0, express_rate_limit_1.rateLimit)({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 5, // 5 attempts
    message: {
        success: false,
        message: 'Too many login attempts',
        error: 'Please try again after 15 minutes'
    },
});
/**
 * Admin login
 * @param req Request
 * @param res Response
 */
const login = async (req, res) => {
    try {
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            return (0, apiResponse_1.sendValidationError)(res, errors.array());
        }
        const { email, password } = req.body;
        // Find admin
        const admin = await Admin_1.Admin.findByEmail(email);
        if (!admin) {
            return (0, apiResponse_1.sendUnauthorized)(res, 'Invalid credentials');
        }
        // Check if account is locked
        if (admin.lockUntil && admin.lockUntil > new Date()) {
            return (0, apiResponse_1.sendError)(res, 'Account is locked', 'Please try again later or reset your password', 423 // Locked
            );
        }
        // Check password
        if (!admin.password) {
            return (0, apiResponse_1.sendUnauthorized)(res, 'Invalid credentials');
        }
        const isMatch = await bcryptjs_1.default.compare(password, admin.password);
        if (!isMatch) {
            // Increment failed login attempts
            const newAttempts = admin.failedLoginAttempts + 1;
            const lockUntil = newAttempts >= 5 ? new Date(Date.now() + 30 * 60 * 1000) : undefined;
            await Admin_1.Admin.updateFailedLoginAttempts(admin.id, newAttempts, lockUntil);
            return (0, apiResponse_1.sendUnauthorized)(res, 'Invalid credentials');
        }
        // Reset failed login attempts and update last login
        await Admin_1.Admin.updateLastLogin(admin.id);
        // Parse privileges if stored as string
        const privileges = typeof admin.privileges === 'string'
            ? JSON.parse(admin.privileges)
            : admin.privileges;
        // Generate token
        const jwtSecret = process.env.JWT_SECRET || 'your-secret-key';
        const jwtOptions = { expiresIn: '1d' }; // Fixed expiration time
        const token = jsonwebtoken_1.default.sign({
            id: admin.id,
            email: admin.email,
            role: admin.role,
            isMainAdmin: admin.isMainAdmin
        }, jwtSecret, jwtOptions);
        // Set token in HTTP-only cookie
        res.cookie('token', token, {
            httpOnly: true,
            secure: process.env.NODE_ENV === 'production',
            sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax', // Use 'none' in production with secure=true, 'lax' in development
            maxAge: 24 * 60 * 60 * 1000, // 1 day
            path: '/'
        });
        return (0, apiResponse_1.sendSuccess)(res, {
            admin: {
                id: admin.id,
                name: admin.name,
                email: admin.email,
                role: admin.role,
                isMainAdmin: admin.isMainAdmin,
                privileges,
                twoFactorEnabled: admin.twoFactorEnabled
            }
        }, 'Login successful');
    }
    catch (error) {
        console.error('Admin login error:', error);
        return (0, apiResponse_1.sendError)(res, 'Login failed', error);
    }
};
exports.login = login;
/**
 * Admin logout
 * @param req Request
 * @param res Response
 */
const logout = async (req, res) => {
    try {
        // Clear the token cookie
        res.clearCookie('token', {
            httpOnly: true,
            secure: process.env.NODE_ENV === 'production',
            sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax', // Use 'none' in production with secure=true, 'lax' in development
            path: '/'
        });
        return (0, apiResponse_1.sendSuccess)(res, null, 'Logout successful');
    }
    catch (error) {
        console.error('Admin logout error:', error);
        return (0, apiResponse_1.sendError)(res, 'Logout failed', error);
    }
};
exports.logout = logout;
/**
 * Get current admin profile
 * @param req Request
 * @param res Response
 */
const getCurrentAdmin = async (req, res) => {
    var _a;
    try {
        const adminId = (_a = req.user) === null || _a === void 0 ? void 0 : _a.id;
        if (!adminId) {
            return (0, apiResponse_1.sendUnauthorized)(res, 'Admin not authenticated');
        }
        const admin = await Admin_1.Admin.findById(adminId);
        if (!admin) {
            return (0, apiResponse_1.sendNotFound)(res, 'Admin not found');
        }
        // Parse privileges if stored as string
        const privileges = typeof admin.privileges === 'string'
            ? JSON.parse(admin.privileges)
            : admin.privileges;
        return (0, apiResponse_1.sendSuccess)(res, {
            admin: {
                id: admin.id,
                name: admin.name,
                email: admin.email,
                role: admin.role,
                isMainAdmin: admin.isMainAdmin,
                privileges,
                twoFactorEnabled: admin.twoFactorEnabled,
                lastLogin: admin.lastLogin,
                createdAt: admin.createdAt,
                updatedAt: admin.updatedAt
            }
        }, 'Admin profile retrieved successfully');
    }
    catch (error) {
        console.error('Get admin profile error:', error);
        return (0, apiResponse_1.sendError)(res, 'Failed to retrieve admin profile', error);
    }
};
exports.getCurrentAdmin = getCurrentAdmin;
/**
 * Get all admins (main admin only)
 * @param req Request
 * @param res Response
 */
const getAdmins = async (req, res) => {
    var _a;
    try {
        // This endpoint should only be accessible by main admin
        if (!((_a = req.user) === null || _a === void 0 ? void 0 : _a.isMainAdmin)) {
            return (0, apiResponse_1.sendForbidden)(res, 'Only main admin can access this resource');
        }
        const admins = await Admin_1.Admin.findAll();
        // Process admins for response (privileges are already parsed in the model)
        const processedAdmins = admins.map((admin) => {
            var _a, _b, _c;
            return ({
                ...admin,
                password: undefined, // Remove password from response
                createdAt: (_a = admin.createdAt) === null || _a === void 0 ? void 0 : _a.toISOString(),
                updatedAt: (_b = admin.updatedAt) === null || _b === void 0 ? void 0 : _b.toISOString(),
                lastLogin: ((_c = admin.lastLogin) === null || _c === void 0 ? void 0 : _c.toISOString()) || null
            });
        });
        return (0, apiResponse_1.sendSuccess)(res, { admins: processedAdmins }, 'Admins retrieved successfully');
    }
    catch (error) {
        console.error('Get admins error:', error);
        return (0, apiResponse_1.sendError)(res, 'Failed to retrieve admins', error);
    }
};
exports.getAdmins = getAdmins;
