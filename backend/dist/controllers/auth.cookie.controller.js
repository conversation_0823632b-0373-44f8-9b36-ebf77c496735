"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getProfile = exports.logout = exports.login = exports.register = exports.resetPasswordLimiter = exports.loginLimiter = void 0;
const express_validator_1 = require("express-validator");
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const bcryptjs_1 = __importDefault(require("bcryptjs"));
const User_1 = require("../models/User");
const express_rate_limit_1 = require("express-rate-limit");
const apiResponse_1 = require("../utils/apiResponse");
const authLogger_1 = __importStar(require("../utils/authLogger"));
// Rate limiting for login attempts
exports.loginLimiter = (0, express_rate_limit_1.rateLimit)({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 5, // 5 attempts
    message: {
        success: false,
        message: 'Too many login attempts',
        error: 'Please try again after 15 minutes'
    },
});
// Rate limiting for password reset
exports.resetPasswordLimiter = (0, express_rate_limit_1.rateLimit)({
    windowMs: 60 * 60 * 1000, // 1 hour
    max: 3, // 3 attempts
    message: {
        success: false,
        message: 'Too many password reset attempts',
        error: 'Please try again after 1 hour'
    },
});
/**
 * Register a new user
 * @param req Request
 * @param res Response
 */
const register = async (req, res) => {
    try {
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            return (0, apiResponse_1.sendValidationError)(res, errors.array());
        }
        const { name, email, password } = req.body;
        // Check if user already exists
        const existingUser = await User_1.User.findByEmail(email);
        if (existingUser) {
            return (0, apiResponse_1.sendError)(res, 'User already exists', null, 409);
        }
        // Create new user
        const user = await User_1.User.create({
            name,
            email,
            password, // User.create will hash the password
            role: 'user',
            failedLoginAttempts: 0,
            mustChangePassword: false
        });
        // Generate token
        const jwtSecret = process.env.JWT_SECRET || 'your-secret-key';
        const jwtOptions = { expiresIn: '1d' }; // Fixed expiration time
        const token = jsonwebtoken_1.default.sign({ id: user.id, role: user.role }, jwtSecret, jwtOptions);
        // Set token in HTTP-only cookie
        res.cookie('token', token, {
            httpOnly: true,
            secure: process.env.NODE_ENV === 'production',
            sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax', // Use 'none' in production with secure=true, 'lax' in development
            maxAge: 24 * 60 * 60 * 1000, // 1 day
            path: '/'
        });
        return (0, apiResponse_1.sendSuccess)(res, {
            user: {
                id: user.id,
                name: user.name,
                email: user.email,
                role: user.role
            }
        }, 'Registration successful', 201);
    }
    catch (error) {
        console.error('Registration error:', error);
        return (0, apiResponse_1.sendError)(res, 'Registration failed', error);
    }
};
exports.register = register;
/**
 * Login a user
 * @param req Request
 * @param res Response
 */
const login = async (req, res) => {
    try {
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            return (0, apiResponse_1.sendValidationError)(res, errors.array());
        }
        const { email, password } = req.body;
        // Find user
        const user = await User_1.User.findByEmail(email);
        if (!user) {
            return (0, apiResponse_1.sendUnauthorized)(res, 'Invalid credentials');
        }
        // Check if account is locked
        if (user.lockUntil && user.lockUntil > new Date()) {
            // Log account locked event
            (0, authLogger_1.logAccountLocked)(user.id, user.email, req.ip || 'unknown');
            return (0, apiResponse_1.sendError)(res, 'Account is locked', 'Please try again later or reset your password', 423 // Locked
            );
        }
        // Check password
        const isMatch = await bcryptjs_1.default.compare(password, user.password);
        if (!isMatch) {
            // Increment failed login attempts
            const newFailedAttempts = user.failedLoginAttempts + 1;
            const shouldLock = newFailedAttempts >= 5;
            const lockUntil = shouldLock ? new Date(Date.now() + 30 * 60 * 1000) : undefined; // Lock for 30 minutes after 5 failed attempts
            await User_1.User.update(user.id, {
                failedLoginAttempts: newFailedAttempts,
                lockUntil
            });
            // Log login failure
            (0, authLogger_1.logLoginFailure)(user.email, 'Invalid password', req.ip || 'unknown', newFailedAttempts, req.headers['user-agent']);
            // If account is now locked, log that too
            if (shouldLock) {
                (0, authLogger_1.logAccountLocked)(user.id, user.email, req.ip || 'unknown');
            }
            return (0, apiResponse_1.sendUnauthorized)(res, 'Invalid credentials');
        }
        // Reset failed login attempts and update last login
        await User_1.User.update(user.id, {
            failedLoginAttempts: 0,
            lockUntil: undefined,
            lastLogin: new Date()
        });
        // Generate token
        const jwtSecret = process.env.JWT_SECRET || 'your-secret-key';
        const jwtOptions = { expiresIn: '1d' }; // Fixed expiration time
        const token = jsonwebtoken_1.default.sign({ id: user.id, role: user.role }, jwtSecret, jwtOptions);
        // Set token in HTTP-only cookie
        res.cookie('token', token, {
            httpOnly: true,
            secure: process.env.NODE_ENV === 'production',
            sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax', // Use 'none' in production with secure=true, 'lax' in development
            maxAge: 24 * 60 * 60 * 1000, // 1 day
            path: '/'
        });
        // Log successful login
        (0, authLogger_1.logLoginSuccess)(user.id, user.email, user.role, req.ip || 'unknown', req.headers['user-agent']);
        return (0, apiResponse_1.sendSuccess)(res, {
            user: {
                id: user.id,
                name: user.name,
                email: user.email,
                role: user.role
            }
        }, 'Login successful');
    }
    catch (error) {
        console.error('Login error:', error);
        return (0, apiResponse_1.sendError)(res, 'Login failed', error);
    }
};
exports.login = login;
/**
 * Logout a user
 * @param req Request
 * @param res Response
 */
const logout = async (req, res) => {
    var _a, _b, _c;
    try {
        // Get user info before clearing cookie
        const userId = (_a = req.user) === null || _a === void 0 ? void 0 : _a.id;
        const userEmail = (_b = req.user) === null || _b === void 0 ? void 0 : _b.email;
        const userRole = (_c = req.user) === null || _c === void 0 ? void 0 : _c.role;
        // Clear the token cookie
        res.clearCookie('token', {
            httpOnly: true,
            secure: process.env.NODE_ENV === 'production',
            sameSite: process.env.NODE_ENV === 'production' ? 'none' : 'lax', // Use 'none' in production with secure=true, 'lax' in development
            path: '/'
        });
        // Log logout event if we have user info
        if (userId && userEmail) {
            authLogger_1.default.logAuthEvent(authLogger_1.AuthEventType.LOGOUT, `User logged out: ${userEmail}`, { userId, email: userEmail, role: userRole, ip: req.ip }, authLogger_1.SecurityLevel.INFO);
        }
        return (0, apiResponse_1.sendSuccess)(res, null, 'Logout successful');
    }
    catch (error) {
        console.error('Logout error:', error);
        return (0, apiResponse_1.sendError)(res, 'Logout failed', error);
    }
};
exports.logout = logout;
/**
 * Get the current user's profile
 * @param req Request
 * @param res Response
 */
const getProfile = async (req, res) => {
    var _a;
    try {
        const userId = (_a = req.user) === null || _a === void 0 ? void 0 : _a.id;
        if (!userId) {
            return (0, apiResponse_1.sendUnauthorized)(res, 'User not authenticated');
        }
        const user = await User_1.User.findById(userId);
        if (!user) {
            return (0, apiResponse_1.sendNotFound)(res, 'User not found');
        }
        return (0, apiResponse_1.sendSuccess)(res, { user }, 'Profile retrieved successfully');
    }
    catch (error) {
        console.error('Get profile error:', error);
        return (0, apiResponse_1.sendError)(res, 'Failed to retrieve profile', error);
    }
};
exports.getProfile = getProfile;
