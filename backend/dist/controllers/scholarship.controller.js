"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.bulkImportScholarships = exports.deleteScholarship = exports.updateScholarship = exports.getScholarshipById = exports.searchScholarships = exports.getScholarships = exports.createScholarship = void 0;
const express_validator_1 = require("express-validator");
const Scholarship_1 = require("../models/Scholarship");
const Newsletter_1 = require("../models/Newsletter");
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
const email_1 = require("../utils/email");
const apiResponse_1 = require("../utils/apiResponse");
const dateUtils_1 = __importDefault(require("../utils/dateUtils"));
// Using PostgreSQL models directly instead of Prisma
// Create scholarship controller
const createScholarship = async (req, res) => {
    var _a, _b, _c;
    try {
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({ errors: errors.array() });
        }
        // Log the request body and file
        console.log('Create scholarship request body:', req.body);
        console.log('Create scholarship request file:', req.file);
        // Parse fields that need conversion
        let scholarshipData = {
            ...req.body,
        };
        // Handle created_by field based on user type
        if (((_a = req.user) === null || _a === void 0 ? void 0 : _a.role) === 'admin' || ((_b = req.user) === null || _b === void 0 ? void 0 : _b.role) === 'super_admin') {
            // Admin created scholarship
            scholarshipData.createdByAdmin = req.user.id;
            scholarshipData.createdBy = null;
        }
        else {
            // User created scholarship
            scholarshipData.createdBy = (_c = req.user) === null || _c === void 0 ? void 0 : _c.id;
            scholarshipData.createdByAdmin = null;
        }
        // Convert isOpen to boolean if it's a string
        if (typeof scholarshipData.isOpen === 'string') {
            scholarshipData.isOpen = scholarshipData.isOpen === 'true';
        }
        // Ensure deadline is a valid Date
        if (scholarshipData.deadline && typeof scholarshipData.deadline === 'string') {
            try {
                // Try to parse the date
                const date = new Date(scholarshipData.deadline);
                if (!isNaN(date.getTime())) {
                    scholarshipData.deadline = date;
                }
            }
            catch (err) {
                console.error('Error parsing deadline date:', err);
                return res.status(400).json({ message: 'Invalid deadline date format' });
            }
        }
        // Make sure the thumbnail field is properly set if a file was uploaded
        // This should be handled by the processUploadedFile middleware, but we'll double-check here
        if (req.file && !scholarshipData.thumbnail) {
            scholarshipData.thumbnail = `/uploads/scholarships/${req.file.filename}`;
            console.log('Setting thumbnail path from uploaded file:', scholarshipData.thumbnail);
        }
        console.log('Processed scholarship data for creation:', scholarshipData);
        // Create the scholarship in the database
        const scholarship = await Scholarship_1.Scholarship.create(scholarshipData);
        console.log('Scholarship created successfully:', scholarship.id);
        // Send notification to all newsletter subscribers
        try {
            // Get all newsletter subscribers
            const subscribers = await Newsletter_1.Newsletter.findAll();
            if (subscribers.length > 0) {
                // Send notification in the background (don't await)
                (0, email_1.sendNewScholarshipNotification)(subscribers.map(s => ({ email: s.email })), scholarship.title, scholarship.description || '', scholarship.id).catch(err => {
                    console.error('Error sending scholarship notifications:', err);
                });
                console.log(`Queued notifications to ${subscribers.length} subscribers about new scholarship`);
            }
        }
        catch (notificationError) {
            // Log error but don't fail the scholarship creation
            console.error('Error preparing scholarship notifications:', notificationError);
        }
        res.status(201).json(scholarship);
    }
    catch (error) {
        console.error('Create scholarship error:', error);
        res.status(500).json({ message: 'Server error', error: error.message });
    }
};
exports.createScholarship = createScholarship;
// Get all scholarships controller with pagination
const getScholarships = async (req, res) => {
    try {
        const { level, country, isOpen, page = '1', limit = '10', sortBy = 'createdAt', sortOrder = 'desc' } = req.query;
        const where = {};
        // Parse pagination parameters
        const pageNumber = parseInt(page, 10);
        const limitNumber = parseInt(limit, 10);
        const skip = (pageNumber - 1) * limitNumber;
        // Apply filters
        if (level)
            where.level = level;
        if (country)
            where.country = country;
        if (isOpen !== undefined)
            where.isOpen = isOpen === 'true';
        // Validate sortBy field to prevent injection
        const validSortFields = ['createdAt', 'title', 'deadline', 'level', 'country'];
        const sortField = validSortFields.includes(sortBy) ? sortBy : 'createdAt';
        // Validate sortOrder
        const order = (sortOrder === null || sortOrder === void 0 ? void 0 : sortOrder.toLowerCase()) === 'asc' ? 'asc' : 'desc';
        // Get total count for pagination
        const totalCount = await Scholarship_1.Scholarship.count(where);
        // Get paginated scholarships
        const scholarships = await Scholarship_1.Scholarship.findAll({
            limit: limitNumber,
            offset: skip,
            ...where
        });
        // Process scholarships to add computed fields and format dates
        const processedScholarships = scholarships.map((scholarship) => ({
            ...scholarship,
            createdAt: scholarship.createdAt.toISOString(),
            updatedAt: scholarship.updatedAt.toISOString(),
            deadline: scholarship.deadline.toISOString(),
            // Add additional fields for the frontend
            isExpired: dateUtils_1.default.isDatePast(scholarship.deadline),
            daysRemaining: dateUtils_1.default.getDaysRemaining(scholarship.deadline),
            formattedDeadline: dateUtils_1.default.formatDate(scholarship.deadline),
        }));
        // Calculate pagination metadata
        const totalPages = Math.ceil(totalCount / limitNumber);
        // Return paginated response
        return (0, apiResponse_1.sendPaginatedSuccess)(res, processedScholarships, {
            page: pageNumber,
            limit: limitNumber,
            total: totalCount,
            totalPages
        }, 'Scholarships retrieved successfully');
    }
    catch (error) {
        console.error('Get scholarships error:', error);
        return (0, apiResponse_1.sendError)(res, 'Failed to retrieve scholarships', error);
    }
};
exports.getScholarships = getScholarships;
// Search scholarships controller with pagination
const searchScholarships = async (req, res) => {
    try {
        const { q, level, country, isOpen, page = '1', limit = '10', sortBy = 'createdAt', sortOrder = 'desc' } = req.query;
        const where = {};
        // Parse pagination parameters
        const pageNumber = parseInt(page, 10);
        const limitNumber = parseInt(limit, 10);
        const skip = (pageNumber - 1) * limitNumber;
        // Apply search and filters
        if (q) {
            // Use more efficient search with indexes
            where.OR = [
                { title: { contains: q, mode: 'insensitive' } },
                { description: { contains: q, mode: 'insensitive' } }
            ];
        }
        // Apply filters
        if (level)
            where.level = level;
        if (country)
            where.country = country;
        if (isOpen !== undefined)
            where.isOpen = isOpen === 'true';
        // Validate sortBy field to prevent injection
        const validSortFields = ['createdAt', 'title', 'deadline', 'level', 'country'];
        const sortField = validSortFields.includes(sortBy) ? sortBy : 'createdAt';
        // Validate sortOrder
        const order = (sortOrder === null || sortOrder === void 0 ? void 0 : sortOrder.toLowerCase()) === 'asc' ? 'asc' : 'desc';
        // Use Promise.all to run queries in parallel for better performance
        const [totalCount, scholarships] = await Promise.all([
            // Get total count for pagination
            Scholarship_1.Scholarship.count(where),
            // Get paginated search results
            Scholarship_1.Scholarship.search(q || '', {
                limit: limitNumber,
                offset: skip,
                ...where
            })
        ]);
        // Process scholarships to add computed fields and format dates
        const processedScholarships = scholarships.map((scholarship) => ({
            ...scholarship,
            createdAt: scholarship.createdAt.toISOString(),
            updatedAt: scholarship.updatedAt.toISOString(),
            deadline: scholarship.deadline.toISOString(),
            // Add additional fields for the frontend
            isExpired: dateUtils_1.default.isDatePast(scholarship.deadline),
            daysRemaining: dateUtils_1.default.getDaysRemaining(scholarship.deadline),
            formattedDeadline: dateUtils_1.default.formatDate(scholarship.deadline),
        }));
        // Calculate pagination metadata
        const totalPages = Math.ceil(totalCount / limitNumber);
        // Return paginated response using standardized format
        return (0, apiResponse_1.sendPaginatedSuccess)(res, processedScholarships, {
            page: pageNumber,
            limit: limitNumber,
            total: totalCount,
            totalPages
        }, 'Scholarships retrieved successfully');
    }
    catch (error) {
        console.error('Search scholarships error:', error);
        return (0, apiResponse_1.sendError)(res, 'Failed to search scholarships', error);
    }
};
exports.searchScholarships = searchScholarships;
// Get scholarship by ID controller
const getScholarshipById = async (req, res) => {
    var _a, _b;
    try {
        const id = parseInt(req.params.id);
        // Validate ID
        if (isNaN(id) || id <= 0) {
            return (0, apiResponse_1.sendError)(res, 'Invalid scholarship ID', null, 400);
        }
        const scholarship = await Scholarship_1.Scholarship.findById(id);
        if (!scholarship) {
            return (0, apiResponse_1.sendNotFound)(res, 'Scholarship not found');
        }
        // Process dates for consistent format
        const processedScholarship = {
            ...scholarship,
            createdAt: (_a = scholarship.createdAt) === null || _a === void 0 ? void 0 : _a.toISOString(),
            updatedAt: (_b = scholarship.updatedAt) === null || _b === void 0 ? void 0 : _b.toISOString(),
            deadline: scholarship.deadline.toISOString(),
            // Add additional fields for the frontend
            isExpired: dateUtils_1.default.isDatePast(scholarship.deadline),
            daysRemaining: dateUtils_1.default.getDaysRemaining(scholarship.deadline),
            formattedDeadline: dateUtils_1.default.formatDate(scholarship.deadline),
        };
        return (0, apiResponse_1.sendSuccess)(res, processedScholarship, 'Scholarship retrieved successfully');
    }
    catch (error) {
        console.error('Get scholarship by ID error:', error);
        return (0, apiResponse_1.sendError)(res, 'Failed to retrieve scholarship', error);
    }
};
exports.getScholarshipById = getScholarshipById;
// Update scholarship controller
const updateScholarship = async (req, res) => {
    var _a, _b, _c, _d, _e, _f, _g, _h;
    try {
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            return (0, apiResponse_1.sendValidationError)(res, errors.array());
        }
        // Log the request body and file
        console.log('Update scholarship request body:', req.body);
        console.log('Update scholarship file:', req.file);
        const scholarshipId = parseInt(req.params.id);
        if (isNaN(scholarshipId)) {
            return (0, apiResponse_1.sendError)(res, 'Invalid scholarship ID', null, 400);
        }
        const scholarship = await Scholarship_1.Scholarship.findById(scholarshipId);
        if (!scholarship) {
            return (0, apiResponse_1.sendNotFound)(res, 'Scholarship not found');
        }
        // Check if user is the creator or an admin
        const isCreator = (((_a = req.user) === null || _a === void 0 ? void 0 : _a.role) === 'admin' || ((_b = req.user) === null || _b === void 0 ? void 0 : _b.role) === 'super_admin')
            ? scholarship.createdByAdmin === ((_c = req.user) === null || _c === void 0 ? void 0 : _c.id)
            : scholarship.createdBy === ((_d = req.user) === null || _d === void 0 ? void 0 : _d.id);
        const isAdmin = ((_e = req.user) === null || _e === void 0 ? void 0 : _e.role) === 'admin' || ((_f = req.user) === null || _f === void 0 ? void 0 : _f.role) === 'super_admin';
        if (!isCreator && !isAdmin) {
            return (0, apiResponse_1.sendForbidden)(res, 'Not authorized to update this scholarship');
        }
        // Parse fields that need conversion
        let updateData = {
            ...req.body
        };
        // Convert isOpen to boolean if it's a string
        if (typeof updateData.isOpen === 'string') {
            updateData.isOpen = updateData.isOpen === 'true';
        }
        // Ensure deadline is a valid Date
        if (updateData.deadline && typeof updateData.deadline === 'string') {
            try {
                // Try to parse the date
                const date = new Date(updateData.deadline);
                if (!isNaN(date.getTime())) {
                    updateData.deadline = date;
                }
                else {
                    return (0, apiResponse_1.sendError)(res, 'Invalid deadline date format', null, 400);
                }
            }
            catch (err) {
                console.error('Error parsing deadline date:', err);
                return (0, apiResponse_1.sendError)(res, 'Invalid deadline date format', err, 400);
            }
        }
        // Make sure the thumbnail field is properly set if a file was uploaded
        if (req.file) {
            updateData.thumbnail = `/uploads/scholarships/${req.file.filename}`;
            console.log('Setting thumbnail path from uploaded file:', updateData.thumbnail);
        }
        console.log('Processed update data:', updateData);
        // If a new thumbnail was uploaded, we should delete the old one to save space
        if (updateData.thumbnail && scholarship.thumbnail &&
            updateData.thumbnail !== scholarship.thumbnail &&
            scholarship.thumbnail.startsWith('/uploads/')) {
            try {
                // Get the file path relative to the project root
                const oldFilePath = path_1.default.join(__dirname, '../..', scholarship.thumbnail);
                // Check if file exists before attempting to delete
                if (fs_1.default.existsSync(oldFilePath)) {
                    fs_1.default.unlinkSync(oldFilePath);
                    console.log(`Deleted old thumbnail: ${oldFilePath}`);
                }
            }
            catch (err) {
                // Log error but don't fail the update
                console.error('Error deleting old thumbnail:', err);
            }
        }
        const updatedScholarship = await Scholarship_1.Scholarship.update(scholarshipId, updateData);
        if (!updatedScholarship) {
            return (0, apiResponse_1.sendError)(res, 'Failed to update scholarship', null, 500);
        }
        // Process dates for consistent format
        const processedScholarship = {
            ...updatedScholarship,
            createdAt: (_g = updatedScholarship.createdAt) === null || _g === void 0 ? void 0 : _g.toISOString(),
            updatedAt: (_h = updatedScholarship.updatedAt) === null || _h === void 0 ? void 0 : _h.toISOString(),
            deadline: updatedScholarship.deadline.toISOString(),
            // Add additional fields for the frontend
            isExpired: dateUtils_1.default.isDatePast(updatedScholarship.deadline),
            daysRemaining: dateUtils_1.default.getDaysRemaining(updatedScholarship.deadline),
            formattedDeadline: dateUtils_1.default.formatDate(updatedScholarship.deadline),
        };
        console.log('Scholarship updated successfully:', updatedScholarship.id);
        return (0, apiResponse_1.sendSuccess)(res, processedScholarship, 'Scholarship updated successfully');
    }
    catch (error) {
        console.error('Update scholarship error:', error);
        return (0, apiResponse_1.sendError)(res, 'Failed to update scholarship', error);
    }
};
exports.updateScholarship = updateScholarship;
// Delete scholarship controller
const deleteScholarship = async (req, res) => {
    var _a, _b, _c, _d, _e, _f;
    try {
        const scholarshipId = parseInt(req.params.id);
        if (isNaN(scholarshipId) || scholarshipId <= 0) {
            return (0, apiResponse_1.sendError)(res, 'Invalid scholarship ID', null, 400);
        }
        const scholarship = await Scholarship_1.Scholarship.findById(scholarshipId);
        if (!scholarship) {
            return (0, apiResponse_1.sendNotFound)(res, 'Scholarship not found');
        }
        // Check if user is the creator or an admin
        const isCreator = (((_a = req.user) === null || _a === void 0 ? void 0 : _a.role) === 'admin' || ((_b = req.user) === null || _b === void 0 ? void 0 : _b.role) === 'super_admin')
            ? scholarship.createdByAdmin === ((_c = req.user) === null || _c === void 0 ? void 0 : _c.id)
            : scholarship.createdBy === ((_d = req.user) === null || _d === void 0 ? void 0 : _d.id);
        const isAdmin = ((_e = req.user) === null || _e === void 0 ? void 0 : _e.role) === 'admin' || ((_f = req.user) === null || _f === void 0 ? void 0 : _f.role) === 'super_admin';
        if (!isCreator && !isAdmin) {
            return (0, apiResponse_1.sendForbidden)(res, 'Not authorized to delete this scholarship');
        }
        // If scholarship has a thumbnail, delete the file
        if (scholarship.thumbnail && scholarship.thumbnail.startsWith('/uploads/')) {
            try {
                // Get the file path relative to the project root
                const filePath = path_1.default.join(__dirname, '../..', scholarship.thumbnail);
                // Check if file exists before attempting to delete
                if (fs_1.default.existsSync(filePath)) {
                    fs_1.default.unlinkSync(filePath);
                    console.log(`Deleted thumbnail for scholarship ${scholarship.id}: ${filePath}`);
                }
            }
            catch (err) {
                // Log error but don't fail the delete operation
                console.error('Error deleting thumbnail file:', err);
            }
        }
        // Delete the scholarship from the database
        await Scholarship_1.Scholarship.delete(scholarshipId);
        // Invalidate cache for scholarships
        try {
            const apiCache = require('../middleware/apiCache.middleware').default;
            apiCache.invalidateCache('/api/scholarships');
        }
        catch (cacheError) {
            console.error('Error invalidating cache:', cacheError);
        }
        return (0, apiResponse_1.sendSuccess)(res, { id: scholarshipId }, 'Scholarship deleted successfully');
    }
    catch (error) {
        console.error('Delete scholarship error:', error);
        return (0, apiResponse_1.sendError)(res, 'Failed to delete scholarship', error);
    }
};
exports.deleteScholarship = deleteScholarship;
// Bulk import scholarships controller
const bulkImportScholarships = async (req, res) => {
    var _a, _b, _c;
    try {
        // Validate that we have an array of scholarships
        const { scholarships } = req.body;
        if (!Array.isArray(scholarships) || scholarships.length === 0) {
            return (0, apiResponse_1.sendError)(res, 'Invalid request format', 'Expected an array of scholarships', 400);
        }
        console.log(`Processing bulk import of ${scholarships.length} scholarships`);
        // Track results
        const results = {
            success: 0,
            failures: 0,
            details: []
        };
        // Process each scholarship
        for (const scholarshipData of scholarships) {
            try {
                // Basic validation
                if (!scholarshipData.title || !scholarshipData.description || !scholarshipData.deadline) {
                    results.failures++;
                    results.details.push({
                        title: scholarshipData.title || 'Unknown',
                        status: 'error',
                        message: 'Missing required fields (title, description, or deadline)'
                    });
                    continue;
                }
                // Prepare data for database
                const processedData = {
                    ...scholarshipData,
                };
                // Handle created_by field based on user type
                if (((_a = req.user) === null || _a === void 0 ? void 0 : _a.role) === 'admin' || ((_b = req.user) === null || _b === void 0 ? void 0 : _b.role) === 'super_admin') {
                    // Admin created scholarship
                    processedData.createdByAdmin = req.user.id;
                    processedData.createdBy = null;
                }
                else {
                    // User created scholarship
                    processedData.createdBy = (_c = req.user) === null || _c === void 0 ? void 0 : _c.id;
                    processedData.createdByAdmin = null;
                }
                // Convert isOpen to boolean if it's a string
                if (typeof processedData.isOpen === 'string') {
                    processedData.isOpen = processedData.isOpen === 'true';
                }
                else if (processedData.isOpen === undefined) {
                    processedData.isOpen = true; // Default to true if not specified
                }
                // Ensure deadline is a valid Date
                if (processedData.deadline && typeof processedData.deadline === 'string') {
                    try {
                        const date = new Date(processedData.deadline);
                        if (isNaN(date.getTime())) {
                            throw new Error('Invalid date format');
                        }
                        processedData.deadline = date;
                    }
                    catch (err) {
                        results.failures++;
                        results.details.push({
                            title: processedData.title,
                            status: 'error',
                            message: 'Invalid deadline date format'
                        });
                        continue;
                    }
                }
                // Create the scholarship in the database
                const scholarship = await Scholarship_1.Scholarship.create(processedData);
                results.success++;
                results.details.push({
                    title: scholarship.title,
                    status: 'success',
                    id: scholarship.id
                });
                console.log(`Successfully imported scholarship: ${scholarship.title} (ID: ${scholarship.id})`);
            }
            catch (error) {
                console.error(`Error importing scholarship ${scholarshipData.title}:`, error);
                results.failures++;
                results.details.push({
                    title: scholarshipData.title || 'Unknown',
                    status: 'error',
                    message: error.message
                });
            }
        }
        // Invalidate cache for scholarships if any were successfully imported
        if (results.success > 0) {
            try {
                const apiCache = require('../middleware/apiCache.middleware').default;
                apiCache.invalidateCache('/api/scholarships');
            }
            catch (cacheError) {
                console.error('Error invalidating cache:', cacheError);
            }
        }
        // Return results using standardized format
        return (0, apiResponse_1.sendSuccess)(res, {
            results,
            summary: {
                total: scholarships.length,
                success: results.success,
                failures: results.failures
            }
        }, `Bulk import completed. ${results.success} successful, ${results.failures} failed.`);
    }
    catch (error) {
        console.error('Bulk import error:', error);
        return (0, apiResponse_1.sendError)(res, 'Failed to process bulk import', error);
    }
};
exports.bulkImportScholarships = bulkImportScholarships;
