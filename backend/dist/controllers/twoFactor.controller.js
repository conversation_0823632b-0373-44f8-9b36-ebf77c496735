"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getTwoFactorStatus = exports.disableTwoFactor = exports.verifyTwoFactorToken = exports.verifyAndEnableTwoFactor = exports.initializeTwoFactor = void 0;
const Admin_1 = require("../models/Admin");
const twoFactor_1 = require("../utils/twoFactor");
// Initialize 2FA setup
const initializeTwoFactor = async (req, res) => {
    var _a;
    try {
        const adminId = (_a = req.user) === null || _a === void 0 ? void 0 : _a.id;
        if (!adminId) {
            return res.status(401).json({ message: 'Unauthorized' });
        }
        // Get admin
        const admin = await Admin_1.Admin.findById(adminId);
        if (!admin) {
            return res.status(404).json({ message: 'Admin not found' });
        }
        // Check if 2FA is already enabled
        if (admin.twoFactorEnabled) {
            return res.status(400).json({ message: '2FA is already enabled' });
        }
        // Generate a new secret
        const secret = (0, twoFactor_1.generateSecret)(admin.email);
        // Generate QR code
        const qrCode = await (0, twoFactor_1.generateQRCode)(secret, admin.email);
        // Save temporary secret
        await Admin_1.Admin.update(adminId, { twoFactorTempSecret: secret });
        res.json({
            message: '2FA initialization successful',
            qrCode,
            secret
        });
    }
    catch (error) {
        console.error('Initialize 2FA error:', error);
        res.status(500).json({ message: 'Server error' });
    }
};
exports.initializeTwoFactor = initializeTwoFactor;
// Verify and enable 2FA
const verifyAndEnableTwoFactor = async (req, res) => {
    var _a;
    try {
        const { token } = req.body;
        const adminId = (_a = req.user) === null || _a === void 0 ? void 0 : _a.id;
        if (!adminId) {
            return res.status(401).json({ message: 'Unauthorized' });
        }
        if (!token) {
            return res.status(400).json({ message: 'Token is required' });
        }
        // Get admin
        const admin = await prisma.admin.findUnique({
            where: { id: adminId }
        });
        if (!admin) {
            return res.status(404).json({ message: 'Admin not found' });
        }
        // Check if 2FA is already enabled
        if (admin.twoFactorEnabled) {
            return res.status(400).json({ message: '2FA is already enabled' });
        }
        // Check if temporary secret exists
        if (!admin.twoFactorTempSecret) {
            return res.status(400).json({ message: '2FA setup not initialized' });
        }
        // Verify token
        const isValid = (0, twoFactor_1.verifyToken)(token, admin.twoFactorTempSecret);
        if (!isValid) {
            return res.status(400).json({ message: 'Invalid token' });
        }
        // Generate backup codes
        const backupCodes = (0, twoFactor_1.generateBackupCodes)();
        // Enable 2FA
        await prisma.admin.update({
            where: { id: adminId },
            data: {
                twoFactorSecret: admin.twoFactorTempSecret,
                twoFactorEnabled: true,
                twoFactorTempSecret: null,
                twoFactorBackupCodes: JSON.stringify(backupCodes)
            }
        });
        res.json({
            message: '2FA enabled successfully',
            backupCodes
        });
    }
    catch (error) {
        console.error('Verify and enable 2FA error:', error);
        res.status(500).json({ message: 'Server error' });
    }
};
exports.verifyAndEnableTwoFactor = verifyAndEnableTwoFactor;
// Verify 2FA token during login
const verifyTwoFactorToken = async (req, res) => {
    try {
        const { adminId, token, isBackupCode = false } = req.body;
        if (!adminId || !token) {
            return res.status(400).json({ message: 'Admin ID and token are required' });
        }
        // Get admin
        const admin = await prisma.admin.findUnique({
            where: { id: adminId }
        });
        if (!admin) {
            return res.status(404).json({ message: 'Admin not found' });
        }
        // Check if 2FA is enabled
        if (!admin.twoFactorEnabled || !admin.twoFactorSecret) {
            return res.status(400).json({ message: '2FA is not enabled for this account' });
        }
        let isValid = false;
        let updatedBackupCodes = null;
        if (isBackupCode) {
            // Verify backup code
            if (!admin.twoFactorBackupCodes) {
                return res.status(400).json({ message: 'No backup codes available' });
            }
            const backupCodes = JSON.parse(admin.twoFactorBackupCodes);
            const result = (0, twoFactor_1.verifyBackupCode)(token, backupCodes);
            isValid = result.valid;
            updatedBackupCodes = result.remainingCodes;
            // Update backup codes if valid
            if (isValid) {
                await prisma.admin.update({
                    where: { id: adminId },
                    data: {
                        twoFactorBackupCodes: JSON.stringify(updatedBackupCodes)
                    }
                });
            }
        }
        else {
            // Verify TOTP token
            isValid = (0, twoFactor_1.verifyToken)(token, admin.twoFactorSecret);
        }
        if (!isValid) {
            return res.status(400).json({ message: 'Invalid token' });
        }
        // Update last login time
        await prisma.admin.update({
            where: { id: adminId },
            data: { lastLogin: new Date() }
        });
        res.json({
            message: '2FA verification successful',
            remainingBackupCodes: updatedBackupCodes ? updatedBackupCodes.length : null
        });
    }
    catch (error) {
        console.error('Verify 2FA token error:', error);
        res.status(500).json({ message: 'Server error' });
    }
};
exports.verifyTwoFactorToken = verifyTwoFactorToken;
// Disable 2FA
const disableTwoFactor = async (req, res) => {
    var _a;
    try {
        const { token } = req.body;
        const adminId = (_a = req.user) === null || _a === void 0 ? void 0 : _a.id;
        if (!adminId) {
            return res.status(401).json({ message: 'Unauthorized' });
        }
        if (!token) {
            return res.status(400).json({ message: 'Token is required' });
        }
        // Get admin
        const admin = await prisma.admin.findUnique({
            where: { id: adminId }
        });
        if (!admin) {
            return res.status(404).json({ message: 'Admin not found' });
        }
        // Check if 2FA is enabled
        if (!admin.twoFactorEnabled || !admin.twoFactorSecret) {
            return res.status(400).json({ message: '2FA is not enabled for this account' });
        }
        // Verify token
        const isValid = (0, twoFactor_1.verifyToken)(token, admin.twoFactorSecret);
        if (!isValid) {
            return res.status(400).json({ message: 'Invalid token' });
        }
        // Disable 2FA
        await prisma.admin.update({
            where: { id: adminId },
            data: {
                twoFactorSecret: null,
                twoFactorEnabled: false,
                twoFactorTempSecret: null,
                twoFactorBackupCodes: null
            }
        });
        res.json({ message: '2FA disabled successfully' });
    }
    catch (error) {
        console.error('Disable 2FA error:', error);
        res.status(500).json({ message: 'Server error' });
    }
};
exports.disableTwoFactor = disableTwoFactor;
// Get 2FA status
const getTwoFactorStatus = async (req, res) => {
    var _a;
    try {
        const adminId = (_a = req.user) === null || _a === void 0 ? void 0 : _a.id;
        if (!adminId) {
            return res.status(401).json({ message: 'Unauthorized' });
        }
        // Get admin
        const admin = await prisma.admin.findUnique({
            where: { id: adminId }
        });
        if (!admin) {
            return res.status(404).json({ message: 'Admin not found' });
        }
        res.json({
            enabled: admin.twoFactorEnabled,
            backupCodesCount: admin.twoFactorBackupCodes
                ? JSON.parse(admin.twoFactorBackupCodes).length
                : 0
        });
    }
    catch (error) {
        console.error('Get 2FA status error:', error);
        res.status(500).json({ message: 'Server error' });
    }
};
exports.getTwoFactorStatus = getTwoFactorStatus;
