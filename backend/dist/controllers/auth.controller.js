"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getProfile = exports.changePassword = exports.resetPassword = exports.forgotPassword = exports.login = exports.register = exports.resetPasswordLimiter = exports.loginLimiter = void 0;
const express_validator_1 = require("express-validator");
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const bcryptjs_1 = __importDefault(require("bcryptjs"));
const User_1 = require("../models/User");
const email_1 = require("../utils/email");
const express_rate_limit_1 = require("express-rate-limit");
const crypto_1 = __importDefault(require("crypto"));
// Rate limiting for login attempts
exports.loginLimiter = (0, express_rate_limit_1.rateLimit)({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 5, // 5 attempts
    message: 'Too many login attempts, please try again after 15 minutes',
});
// Rate limiting for password reset
exports.resetPasswordLimiter = (0, express_rate_limit_1.rateLimit)({
    windowMs: 60 * 60 * 1000, // 1 hour
    max: 3, // 3 attempts
    message: 'Too many password reset attempts, please try again after 1 hour',
});
// Register controller
const register = async (req, res) => {
    try {
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({ errors: errors.array() });
        }
        const { name, email, password } = req.body;
        // Check if user already exists
        const existingUser = await User_1.User.findByEmail(email);
        if (existingUser) {
            return res.status(400).json({ message: 'User already exists' });
        }
        // Hash password
        const hashedPassword = await bcryptjs_1.default.hash(password, 10);
        // Create new user
        const user = await User_1.User.create({
            name,
            email,
            password: hashedPassword,
            role: 'user',
            failedLoginAttempts: 0,
            mustChangePassword: false
        });
        // Generate token
        const token = jsonwebtoken_1.default.sign({ id: user.id, role: user.role }, process.env.JWT_SECRET || 'your-secret-key', { expiresIn: '1d' });
        res.status(201).json({
            success: true,
            token,
            user: {
                id: user.id,
                name: user.name,
                email: user.email,
                role: user.role
            }
        });
    }
    catch (error) {
        console.error('Registration error:', error);
        res.status(500).json({ message: 'Server error' });
    }
};
exports.register = register;
// Login controller
const login = async (req, res) => {
    try {
        const errors = (0, express_validator_1.validationResult)(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({ errors: errors.array() });
        }
        const { email, password } = req.body;
        // Find user
        const user = await User_1.User.findByEmail(email);
        if (!user) {
            return res.status(401).json({ message: 'Invalid credentials' });
        }
        // Check if account is locked
        if (user.lockUntil && user.lockUntil > new Date()) {
            return res.status(401).json({
                message: 'Account is locked. Please try again later or reset your password'
            });
        }
        // Check password
        if (!user.password) {
            return res.status(401).json({ message: 'Invalid credentials' });
        }
        const isMatch = await bcryptjs_1.default.compare(password, user.password);
        if (!isMatch) {
            // Increment failed login attempts
            await User_1.User.update(user.id, {
                failedLoginAttempts: (user.failedLoginAttempts || 0) + 1,
                lockUntil: (user.failedLoginAttempts || 0) >= 4 ? new Date(Date.now() + 30 * 60 * 1000) : undefined
            });
            return res.status(401).json({ message: 'Invalid credentials' });
        }
        // Reset failed login attempts and update last login
        await User_1.User.update(user.id, {
            failedLoginAttempts: 0,
            lockUntil: undefined,
            lastLogin: new Date()
        });
        // Generate token
        const token = jsonwebtoken_1.default.sign({ id: user.id, role: user.role }, process.env.JWT_SECRET || 'your-secret-key', { expiresIn: '1d' });
        res.json({
            success: true,
            token,
            user: {
                id: user.id,
                name: user.name,
                email: user.email,
                role: user.role
            }
        });
    }
    catch (error) {
        console.error('Login error:', error);
        res.status(500).json({ message: 'Server error' });
    }
};
exports.login = login;
// Forgot password controller
const forgotPassword = async (req, res) => {
    try {
        const { email } = req.body;
        const user = await User_1.User.findByEmail(email);
        if (!user) {
            return res.status(404).json({ message: 'User not found' });
        }
        // Generate reset token
        const resetToken = crypto_1.default.randomBytes(32).toString('hex');
        const hashedToken = crypto_1.default
            .createHash('sha256')
            .update(resetToken)
            .digest('hex');
        // Save reset token - Note: This functionality needs to be implemented in User model
        // await User.update(user.id!, {
        //   resetPasswordToken: hashedToken,
        //   resetPasswordExpires: new Date(Date.now() + 60 * 60 * 1000)
        // });
        // Send reset email
        const resetUrl = `${process.env.FRONTEND_URL}/reset-password/${resetToken}`;
        await (0, email_1.sendEmail)({
            to: user.email,
            subject: 'Password Reset Request',
            text: `Please click the following link to reset your password: ${resetUrl}`,
            html: `
        <p>Please click the following link to reset your password:</p>
        <a href="${resetUrl}">Reset Password</a>
        <p>This link will expire in 1 hour.</p>
      `,
        });
        res.json({ message: 'Password reset email sent' });
    }
    catch (error) {
        console.error('Forgot password error:', error);
        res.status(500).json({ message: 'Server error' });
    }
};
exports.forgotPassword = forgotPassword;
// Reset password controller
const resetPassword = async (req, res) => {
    try {
        const { token, password } = req.body;
        // Hash the token
        const hashedToken = crypto_1.default
            .createHash('sha256')
            .update(token)
            .digest('hex');
        // Find user with valid reset token - Note: This functionality needs to be implemented
        // const user = await User.findByResetToken(hashedToken);
        const user = null; // Temporarily disabled
        if (!user) {
            return res.status(400).json({ message: 'Invalid or expired reset token' });
        }
        // Update password - Note: This functionality needs to be implemented
        // const hashedPassword = await bcrypt.hash(password, 10);
        // await User.update(user.id!, {
        //   password: hashedPassword,
        //   resetPasswordToken: undefined,
        //   resetPasswordExpires: undefined
        // });
        res.json({ message: 'Password has been reset' });
    }
    catch (error) {
        console.error('Reset password error:', error);
        res.status(500).json({ message: 'Server error' });
    }
};
exports.resetPassword = resetPassword;
// Change password controller
const changePassword = async (req, res) => {
    try {
        const { currentPassword, newPassword } = req.body;
        const userId = req.user.id;
        const user = await User_1.User.findById(userId);
        if (!user) {
            return res.status(404).json({ message: 'User not found' });
        }
        // Verify current password
        if (!user.password) {
            return res.status(401).json({ message: 'User password not set' });
        }
        const isMatch = await bcryptjs_1.default.compare(currentPassword, user.password);
        if (!isMatch) {
            return res.status(401).json({ message: 'Current password is incorrect' });
        }
        // Update password
        const hashedPassword = await bcryptjs_1.default.hash(newPassword, 10);
        await User_1.User.update(user.id, { password: hashedPassword });
        res.json({ message: 'Password has been changed' });
    }
    catch (error) {
        console.error('Change password error:', error);
        res.status(500).json({ message: 'Server error' });
    }
};
exports.changePassword = changePassword;
// Get profile controller
const getProfile = async (req, res) => {
    try {
        const userId = req.user.id;
        const user = await User_1.User.findById(userId);
        if (!user) {
            return res.status(404).json({ message: 'User not found' });
        }
        res.json({
            success: true,
            user
        });
    }
    catch (error) {
        console.error('Get profile error:', error);
        res.status(500).json({ message: 'Server error' });
    }
};
exports.getProfile = getProfile;
