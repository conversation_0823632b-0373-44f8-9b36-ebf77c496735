"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.validateResetToken = exports.resetPassword = exports.forgotPassword = void 0;
const crypto_1 = __importDefault(require("crypto"));
const bcryptjs_1 = __importDefault(require("bcryptjs"));
const email_1 = require("../utils/email");
const Admin_1 = require("../models/Admin");
// Request password reset - TEMPORARILY DISABLED (needs password reset fields in Admin model)
const forgotPassword = async (req, res) => {
    try {
        const { email } = req.body;
        if (!email) {
            return res.status(400).json({ message: 'Email is required' });
        }
        // Find admin by email
        const admin = await Admin_1.Admin.findByEmail(email);
        if (!admin) {
            // For security reasons, don't reveal that the email doesn't exist
            return res.status(200).json({
                message: 'If your email is registered, you will receive a password reset link'
            });
        }
        // Generate reset token
        const resetToken = crypto_1.default.randomBytes(32).toString('hex');
        const hashedToken = crypto_1.default
            .createHash('sha256')
            .update(resetToken)
            .digest('hex');
        // Save reset token to database - TEMPORARILY DISABLED
        // TODO: Add resetPasswordToken and resetPasswordExpires fields to Admin model
        // await Admin.update(admin.id!, {
        //   resetPasswordToken: hashedToken,
        //   resetPasswordExpires: new Date(Date.now() + 60 * 60 * 1000)
        // });
        // Create reset URL
        const resetUrl = `${process.env.FRONTEND_URL || 'http://localhost:3000'}/admin/reset-password/${resetToken}`;
        // In development, just log the reset URL instead of sending an email
        if (process.env.NODE_ENV !== 'production') {
            console.log('==== DEVELOPMENT MODE ====');
            console.log('Password reset URL:', resetUrl);
            console.log('==========================');
        }
        else {
            // Generate email content
            const { text, html } = (0, email_1.generatePasswordResetEmail)(admin.name, resetUrl);
            // Send email
            await (0, email_1.sendEmail)({
                to: admin.email,
                subject: 'MaBourse Admin Password Reset',
                text,
                html
            });
        }
        res.status(200).json({
            message: 'If your email is registered, you will receive a password reset link'
        });
    }
    catch (error) {
        console.error('Forgot password error:', error);
        res.status(500).json({ message: 'Server error' });
    }
};
exports.forgotPassword = forgotPassword;
// Reset password with token
const resetPassword = async (req, res) => {
    try {
        const { token, password } = req.body;
        if (!token || !password) {
            return res.status(400).json({ message: 'Token and password are required' });
        }
        // Hash the token
        const hashedToken = crypto_1.default
            .createHash('sha256')
            .update(token)
            .digest('hex');
        // Find admin with valid reset token - TEMPORARILY DISABLED
        // TODO: Implement token validation in Admin model
        const admin = null; // await Admin.findByResetToken(hashedToken);
        if (!admin) {
            return res.status(400).json({ message: 'Invalid or expired reset token' });
        }
        // Hash the new password
        const hashedPassword = await bcryptjs_1.default.hash(password, 10);
        // Update admin with new password and clear reset token - TEMPORARILY DISABLED
        // TODO: Implement password update with token clearing in Admin model
        // await Admin.update(admin.id!, {
        //   password: hashedPassword,
        //   resetPasswordToken: null,
        //   resetPasswordExpires: null,
        //   failedLoginAttempts: 0,
        //   lockUntil: null
        // });
        res.status(200).json({ message: 'Password has been reset successfully' });
    }
    catch (error) {
        console.error('Reset password error:', error);
        res.status(500).json({ message: 'Server error' });
    }
};
exports.resetPassword = resetPassword;
// Validate reset token (used to check if token is valid before showing reset form)
const validateResetToken = async (req, res) => {
    try {
        const { token } = req.params;
        if (!token) {
            return res.status(400).json({ message: 'Token is required' });
        }
        // Hash the token
        const hashedToken = crypto_1.default
            .createHash('sha256')
            .update(token)
            .digest('hex');
        // Find admin with valid reset token - TEMPORARILY DISABLED
        // TODO: Implement token validation in Admin model
        const admin = null; // await Admin.findByResetToken(hashedToken);
        if (!admin) {
            return res.status(400).json({ valid: false, message: 'Invalid or expired reset token' });
        }
        res.status(200).json({ valid: true, message: 'Token is valid' });
    }
    catch (error) {
        console.error('Validate reset token error:', error);
        res.status(500).json({ valid: false, message: 'Server error' });
    }
};
exports.validateResetToken = validateResetToken;
