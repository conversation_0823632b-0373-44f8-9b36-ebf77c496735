"use strict";
/**
 * User Model
 *
 * This model handles all database operations for regular users
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.User = void 0;
const bcryptjs_1 = __importDefault(require("bcryptjs"));
const database_1 = require("../config/database");
class User {
    /**
     * Create a new user
     */
    static async create(userData) {
        const hashedPassword = userData.password ? await bcryptjs_1.default.hash(userData.password, 10) : null;
        const result = await (0, database_1.query)(`
      INSERT INTO users (
        name, email, password, role, failed_login_attempts, must_change_password
      ) VALUES ($1, $2, $3, $4, $5, $6)
      RETURNING *
    `, [
            userData.name,
            userData.email,
            hashedPassword,
            userData.role || 'user',
            userData.failedLoginAttempts || 0,
            userData.mustChangePassword || false
        ]);
        return this.mapRowToUser(result.rows[0]);
    }
    /**
     * Find user by ID
     */
    static async findById(id) {
        const result = await (0, database_1.query)('SELECT * FROM users WHERE id = $1', [id]);
        return result.rows.length > 0 ? this.mapRowToUser(result.rows[0]) : null;
    }
    /**
     * Find user by email
     */
    static async findByEmail(email) {
        const result = await (0, database_1.query)('SELECT * FROM users WHERE email = $1', [email]);
        return result.rows.length > 0 ? this.mapRowToUser(result.rows[0]) : null;
    }
    /**
     * Get all users
     */
    static async findAll(limit, offset) {
        let queryText = 'SELECT * FROM users ORDER BY created_at DESC';
        const params = [];
        if (limit) {
            queryText += ' LIMIT $1';
            params.push(limit);
            if (offset) {
                queryText += ' OFFSET $2';
                params.push(offset);
            }
        }
        const result = await (0, database_1.query)(queryText, params);
        return result.rows.map(row => this.mapRowToUser(row));
    }
    /**
     * Update user
     */
    static async update(id, updates) {
        const fields = [];
        const values = [];
        let paramCount = 1;
        // Build dynamic update query
        Object.entries(updates).forEach(([key, value]) => {
            if (value !== undefined && key !== 'id' && key !== 'createdAt' && key !== 'updatedAt') {
                const dbKey = this.camelToSnake(key);
                if (key === 'password' && value) {
                    // Hash password if provided
                    fields.push(`${dbKey} = $${paramCount}`);
                    values.push(bcryptjs_1.default.hashSync(value, 10));
                    // Update password timestamp
                    fields.push(`password_updated_at = CURRENT_TIMESTAMP`);
                }
                else {
                    fields.push(`${dbKey} = $${paramCount}`);
                    values.push(value);
                }
                paramCount++;
            }
        });
        if (fields.length === 0) {
            return this.findById(id);
        }
        values.push(id);
        const result = await (0, database_1.query)(`
      UPDATE users 
      SET ${fields.join(', ')}, updated_at = CURRENT_TIMESTAMP
      WHERE id = $${paramCount}
      RETURNING *
    `, values);
        return result.rows.length > 0 ? this.mapRowToUser(result.rows[0]) : null;
    }
    /**
     * Delete user
     */
    static async delete(id) {
        const result = await (0, database_1.query)('DELETE FROM users WHERE id = $1', [id]);
        return (result.rowCount || 0) > 0;
    }
    /**
     * Verify password
     */
    static async verifyPassword(user, password) {
        if (!user.password)
            return false;
        return bcryptjs_1.default.compare(password, user.password);
    }
    /**
     * Update failed login attempts
     */
    static async updateFailedLoginAttempts(id, attempts, lockUntil) {
        await (0, database_1.query)(`
      UPDATE users 
      SET failed_login_attempts = $1, lock_until = $2, updated_at = CURRENT_TIMESTAMP
      WHERE id = $3
    `, [attempts, lockUntil, id]);
    }
    /**
     * Update last login
     */
    static async updateLastLogin(id) {
        await (0, database_1.query)(`
      UPDATE users 
      SET last_login = CURRENT_TIMESTAMP, failed_login_attempts = 0, lock_until = NULL
      WHERE id = $1
    `, [id]);
    }
    /**
     * Count total users
     */
    static async count() {
        const result = await (0, database_1.query)('SELECT COUNT(*) as count FROM users');
        return parseInt(result.rows[0].count);
    }
    /**
     * Search users by name or email
     */
    static async search(searchTerm, limit) {
        const result = await (0, database_1.query)(`
      SELECT * FROM users 
      WHERE name ILIKE $1 OR email ILIKE $1
      ORDER BY created_at DESC
      ${limit ? 'LIMIT $2' : ''}
    `, limit ? [`%${searchTerm}%`, limit] : [`%${searchTerm}%`]);
        return result.rows.map(row => this.mapRowToUser(row));
    }
    /**
     * Map database row to UserData
     */
    static mapRowToUser(row) {
        return {
            id: row.id,
            name: row.name,
            email: row.email,
            password: row.password,
            role: row.role,
            failedLoginAttempts: row.failed_login_attempts,
            lockUntil: row.lock_until,
            resetPasswordToken: row.reset_password_token,
            resetPasswordExpires: row.reset_password_expires,
            lastLogin: row.last_login,
            createdAt: row.created_at,
            updatedAt: row.updated_at,
            passwordUpdatedAt: row.password_updated_at,
            passwordExpiresAt: row.password_expires_at,
            mustChangePassword: row.must_change_password,
        };
    }
    /**
     * Convert camelCase to snake_case
     */
    static camelToSnake(str) {
        return str.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
    }
}
exports.User = User;
