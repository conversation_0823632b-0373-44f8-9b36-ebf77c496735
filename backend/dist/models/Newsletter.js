"use strict";
/**
 * Newsletter Model
 *
 * This model handles all database operations for newsletter subscriptions
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.Newsletter = void 0;
const database_1 = require("../config/database");
class Newsletter {
    /**
     * Create a new newsletter subscription
     */
    static async create(email) {
        const result = await (0, database_1.query)(`
      INSERT INTO newsletter_subscriptions (email)
      VALUES ($1)
      ON CONFLICT (email) DO NOTHING
      RETURNING *
    `, [email]);
        // If no rows returned, the email already exists
        if (result.rows.length === 0) {
            const existing = await this.findByEmail(email);
            if (existing) {
                return existing;
            }
            throw new Error('Failed to create newsletter subscription');
        }
        return this.mapRowToNewsletter(result.rows[0]);
    }
    /**
     * Find newsletter subscription by email
     */
    static async findByEmail(email) {
        const result = await (0, database_1.query)('SELECT * FROM newsletter_subscriptions WHERE email = $1', [email]);
        return result.rows.length > 0 ? this.mapRowToNewsletter(result.rows[0]) : null;
    }
    /**
     * Get all newsletter subscriptions
     */
    static async findAll(options) {
        let queryText = 'SELECT * FROM newsletter_subscriptions';
        const params = [];
        let paramCount = 1;
        // Add ordering
        const orderBy = (options === null || options === void 0 ? void 0 : options.orderBy) || 'created_at';
        const orderDirection = (options === null || options === void 0 ? void 0 : options.orderDirection) || 'DESC';
        queryText += ` ORDER BY ${orderBy} ${orderDirection}`;
        // Add pagination
        if (options === null || options === void 0 ? void 0 : options.limit) {
            queryText += ` LIMIT $${paramCount}`;
            params.push(options.limit);
            paramCount++;
            if (options === null || options === void 0 ? void 0 : options.offset) {
                queryText += ` OFFSET $${paramCount}`;
                params.push(options.offset);
                paramCount++;
            }
        }
        const result = await (0, database_1.query)(queryText, params);
        return result.rows.map(row => this.mapRowToNewsletter(row));
    }
    /**
     * Search newsletter subscriptions by email
     */
    static async search(searchTerm, options) {
        let queryText = 'SELECT * FROM newsletter_subscriptions WHERE email ILIKE $1';
        const params = [`%${searchTerm}%`];
        let paramCount = 2;
        queryText += ' ORDER BY created_at DESC';
        if (options === null || options === void 0 ? void 0 : options.limit) {
            queryText += ` LIMIT $${paramCount}`;
            params.push(options.limit);
            paramCount++;
            if (options === null || options === void 0 ? void 0 : options.offset) {
                queryText += ` OFFSET $${paramCount}`;
                params.push(options.offset);
                paramCount++;
            }
        }
        const result = await (0, database_1.query)(queryText, params);
        return result.rows.map(row => this.mapRowToNewsletter(row));
    }
    /**
     * Delete newsletter subscription
     */
    static async delete(id) {
        const result = await (0, database_1.query)('DELETE FROM newsletter_subscriptions WHERE id = $1', [id]);
        return (result.rowCount || 0) > 0;
    }
    /**
     * Delete newsletter subscription by email
     */
    static async deleteByEmail(email) {
        const result = await (0, database_1.query)('DELETE FROM newsletter_subscriptions WHERE email = $1', [email]);
        return (result.rowCount || 0) > 0;
    }
    /**
     * Count total newsletter subscriptions
     */
    static async count() {
        const result = await (0, database_1.query)('SELECT COUNT(*) as count FROM newsletter_subscriptions');
        return parseInt(result.rows[0].count);
    }
    /**
     * Check if email is subscribed
     */
    static async isSubscribed(email) {
        const result = await (0, database_1.query)('SELECT 1 FROM newsletter_subscriptions WHERE email = $1', [email]);
        return result.rows.length > 0;
    }
    /**
     * Get recent subscriptions
     */
    static async findRecent(limit = 10) {
        const result = await (0, database_1.query)(`
      SELECT * FROM newsletter_subscriptions 
      ORDER BY created_at DESC
      LIMIT $1
    `, [limit]);
        return result.rows.map(row => this.mapRowToNewsletter(row));
    }
    /**
     * Get subscriptions by date range
     */
    static async findByDateRange(startDate, endDate) {
        const result = await (0, database_1.query)(`
      SELECT * FROM newsletter_subscriptions 
      WHERE created_at >= $1 AND created_at <= $2
      ORDER BY created_at DESC
    `, [startDate, endDate]);
        return result.rows.map(row => this.mapRowToNewsletter(row));
    }
    /**
     * Get all emails for newsletter sending
     */
    static async getAllEmails() {
        const result = await (0, database_1.query)('SELECT email FROM newsletter_subscriptions ORDER BY created_at DESC');
        return result.rows.map(row => row.email);
    }
    /**
     * Bulk create newsletter subscriptions
     */
    static async bulkCreate(emails) {
        let created = 0;
        let existing = 0;
        for (const email of emails) {
            try {
                const result = await (0, database_1.query)(`
          INSERT INTO newsletter_subscriptions (email)
          VALUES ($1)
          ON CONFLICT (email) DO NOTHING
          RETURNING id
        `, [email]);
                if (result.rows.length > 0) {
                    created++;
                }
                else {
                    existing++;
                }
            }
            catch (error) {
                console.error(`Error creating newsletter subscription for ${email}:`, error);
                existing++;
            }
        }
        return { created, existing };
    }
    /**
     * Export all subscriptions to CSV format
     */
    static async exportToCSV() {
        const subscriptions = await this.findAll();
        let csv = 'Email,Subscription Date\n';
        subscriptions.forEach(subscription => {
            const date = subscription.createdAt ? subscription.createdAt.toISOString().split('T')[0] : '';
            csv += `"${subscription.email}","${date}"\n`;
        });
        return csv;
    }
    /**
     * Map database row to NewsletterData
     */
    static mapRowToNewsletter(row) {
        return {
            id: row.id,
            email: row.email,
            createdAt: row.created_at,
        };
    }
}
exports.Newsletter = Newsletter;
