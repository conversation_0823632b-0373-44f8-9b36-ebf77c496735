"use strict";
/**
 * Message Model
 *
 * This model handles all database operations for contact messages
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.Message = void 0;
const database_1 = require("../config/database");
class Message {
    /**
     * Create a new message
     */
    static async create(messageData) {
        const result = await (0, database_1.query)(`
      INSERT INTO messages (name, email, subject, content, status)
      VALUES ($1, $2, $3, $4, $5)
      RETURNING *
    `, [
            messageData.name,
            messageData.email,
            messageData.subject,
            messageData.content,
            messageData.status || 'pending'
        ]);
        return this.mapRowToMessage(result.rows[0]);
    }
    /**
     * Find message by ID
     */
    static async findById(id) {
        const result = await (0, database_1.query)('SELECT * FROM messages WHERE id = $1', [id]);
        return result.rows.length > 0 ? this.mapRowToMessage(result.rows[0]) : null;
    }
    /**
     * Get all messages
     */
    static async findAll(options) {
        let queryText = 'SELECT * FROM messages WHERE 1=1';
        const params = [];
        let paramCount = 1;
        // Add status filter
        if (options === null || options === void 0 ? void 0 : options.status) {
            queryText += ` AND status = $${paramCount}`;
            params.push(options.status);
            paramCount++;
        }
        // Add ordering
        const orderBy = (options === null || options === void 0 ? void 0 : options.orderBy) || 'created_at';
        const orderDirection = (options === null || options === void 0 ? void 0 : options.orderDirection) || 'DESC';
        queryText += ` ORDER BY ${orderBy} ${orderDirection}`;
        // Add pagination
        if (options === null || options === void 0 ? void 0 : options.limit) {
            queryText += ` LIMIT $${paramCount}`;
            params.push(options.limit);
            paramCount++;
            if (options === null || options === void 0 ? void 0 : options.offset) {
                queryText += ` OFFSET $${paramCount}`;
                params.push(options.offset);
                paramCount++;
            }
        }
        const result = await (0, database_1.query)(queryText, params);
        return result.rows.map(row => this.mapRowToMessage(row));
    }
    /**
     * Search messages
     */
    static async search(searchTerm, options) {
        let queryText = `
      SELECT * FROM messages 
      WHERE (name ILIKE $1 OR email ILIKE $1 OR subject ILIKE $1 OR content ILIKE $1)
    `;
        const params = [`%${searchTerm}%`];
        let paramCount = 2;
        if (options === null || options === void 0 ? void 0 : options.status) {
            queryText += ` AND status = $${paramCount}`;
            params.push(options.status);
            paramCount++;
        }
        queryText += ' ORDER BY created_at DESC';
        if (options === null || options === void 0 ? void 0 : options.limit) {
            queryText += ` LIMIT $${paramCount}`;
            params.push(options.limit);
            paramCount++;
            if (options === null || options === void 0 ? void 0 : options.offset) {
                queryText += ` OFFSET $${paramCount}`;
                params.push(options.offset);
                paramCount++;
            }
        }
        const result = await (0, database_1.query)(queryText, params);
        return result.rows.map(row => this.mapRowToMessage(row));
    }
    /**
     * Update message
     */
    static async update(id, updates) {
        const fields = [];
        const values = [];
        let paramCount = 1;
        // Build dynamic update query
        Object.entries(updates).forEach(([key, value]) => {
            if (value !== undefined && key !== 'id' && key !== 'createdAt' && key !== 'updatedAt') {
                fields.push(`${key} = $${paramCount}`);
                values.push(value);
                paramCount++;
            }
        });
        if (fields.length === 0) {
            return this.findById(id);
        }
        values.push(id);
        const result = await (0, database_1.query)(`
      UPDATE messages 
      SET ${fields.join(', ')}, updated_at = CURRENT_TIMESTAMP
      WHERE id = $${paramCount}
      RETURNING *
    `, values);
        return result.rows.length > 0 ? this.mapRowToMessage(result.rows[0]) : null;
    }
    /**
     * Delete message
     */
    static async delete(id) {
        const result = await (0, database_1.query)('DELETE FROM messages WHERE id = $1', [id]);
        return (result.rowCount || 0) > 0;
    }
    /**
     * Count total messages
     */
    static async count(status) {
        let queryText = 'SELECT COUNT(*) as count FROM messages';
        const params = [];
        if (status) {
            queryText += ' WHERE status = $1';
            params.push(status);
        }
        const result = await (0, database_1.query)(queryText, params);
        return parseInt(result.rows[0].count);
    }
    /**
     * Get messages by status
     */
    static async findByStatus(status, limit) {
        const result = await (0, database_1.query)(`
      SELECT * FROM messages 
      WHERE status = $1
      ORDER BY created_at DESC
      ${limit ? 'LIMIT $2' : ''}
    `, limit ? [status, limit] : [status]);
        return result.rows.map(row => this.mapRowToMessage(row));
    }
    /**
     * Mark message as read
     */
    static async markAsRead(id) {
        return this.update(id, { status: 'read' });
    }
    /**
     * Mark message as replied
     */
    static async markAsReplied(id) {
        return this.update(id, { status: 'replied' });
    }
    /**
     * Archive message
     */
    static async archive(id) {
        return this.update(id, { status: 'archived' });
    }
    /**
     * Get recent messages
     */
    static async findRecent(limit = 10) {
        const result = await (0, database_1.query)(`
      SELECT * FROM messages 
      ORDER BY created_at DESC
      LIMIT $1
    `, [limit]);
        return result.rows.map(row => this.mapRowToMessage(row));
    }
    /**
     * Map database row to MessageData
     */
    static mapRowToMessage(row) {
        return {
            id: row.id,
            name: row.name,
            email: row.email,
            subject: row.subject,
            content: row.content,
            status: row.status,
            createdAt: row.created_at,
            updatedAt: row.updated_at,
        };
    }
}
exports.Message = Message;
