"use strict";
/**
 * Admin Model
 *
 * This model handles all database operations for admin users
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Admin = void 0;
const bcryptjs_1 = __importDefault(require("bcryptjs"));
const database_1 = require("../config/database");
class Admin {
    /**
     * Create a new admin
     */
    static async create(adminData) {
        const hashedPassword = adminData.password ? await bcryptjs_1.default.hash(adminData.password, 10) : null;
        const result = await (0, database_1.query)(`
      INSERT INTO admins (
        name, email, password, role, privileges, is_main_admin,
        failed_login_attempts, two_factor_enabled
      ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
      RETURNING *
    `, [
            adminData.name,
            adminData.email,
            hashedPassword,
            adminData.role,
            JSON.stringify(adminData.privileges),
            adminData.isMainAdmin,
            adminData.failedLoginAttempts || 0,
            adminData.twoFactorEnabled || false
        ]);
        return this.mapRowToAdmin(result.rows[0]);
    }
    /**
     * Find admin by ID
     */
    static async findById(id) {
        const result = await (0, database_1.query)('SELECT * FROM admins WHERE id = $1', [id]);
        return result.rows.length > 0 ? this.mapRowToAdmin(result.rows[0]) : null;
    }
    /**
     * Find admin by email
     */
    static async findByEmail(email) {
        const result = await (0, database_1.query)('SELECT * FROM admins WHERE email = $1', [email]);
        return result.rows.length > 0 ? this.mapRowToAdmin(result.rows[0]) : null;
    }
    /**
     * Find main admin
     */
    static async findMainAdmin() {
        const result = await (0, database_1.query)('SELECT * FROM admins WHERE is_main_admin = TRUE LIMIT 1');
        return result.rows.length > 0 ? this.mapRowToAdmin(result.rows[0]) : null;
    }
    /**
     * Get all admins
     */
    static async findAll() {
        const result = await (0, database_1.query)('SELECT * FROM admins ORDER BY created_at DESC');
        return result.rows.map(row => this.mapRowToAdmin(row));
    }
    /**
     * Update admin
     */
    static async update(id, updates) {
        const fields = [];
        const values = [];
        let paramCount = 1;
        // Build dynamic update query
        Object.entries(updates).forEach(([key, value]) => {
            if (value !== undefined && key !== 'id' && key !== 'createdAt' && key !== 'updatedAt') {
                const dbKey = this.camelToSnake(key);
                if (key === 'password' && value) {
                    // Hash password if provided
                    fields.push(`${dbKey} = $${paramCount}`);
                    values.push(bcryptjs_1.default.hashSync(value, 10));
                }
                else if (key === 'privileges' || key === 'twoFactorBackupCodes') {
                    // JSON stringify arrays
                    fields.push(`${dbKey} = $${paramCount}`);
                    values.push(JSON.stringify(value));
                }
                else {
                    fields.push(`${dbKey} = $${paramCount}`);
                    values.push(value);
                }
                paramCount++;
            }
        });
        if (fields.length === 0) {
            return this.findById(id);
        }
        values.push(id);
        const result = await (0, database_1.query)(`
      UPDATE admins 
      SET ${fields.join(', ')}, updated_at = CURRENT_TIMESTAMP
      WHERE id = $${paramCount}
      RETURNING *
    `, values);
        return result.rows.length > 0 ? this.mapRowToAdmin(result.rows[0]) : null;
    }
    /**
     * Delete admin
     */
    static async delete(id) {
        const result = await (0, database_1.query)('DELETE FROM admins WHERE id = $1', [id]);
        return (result.rowCount || 0) > 0;
    }
    /**
     * Verify password
     */
    static async verifyPassword(admin, password) {
        if (!admin.password)
            return false;
        return bcryptjs_1.default.compare(password, admin.password);
    }
    /**
     * Update failed login attempts
     */
    static async updateFailedLoginAttempts(id, attempts, lockUntil) {
        await (0, database_1.query)(`
      UPDATE admins 
      SET failed_login_attempts = $1, lock_until = $2, updated_at = CURRENT_TIMESTAMP
      WHERE id = $3
    `, [attempts, lockUntil, id]);
    }
    /**
     * Update last login
     */
    static async updateLastLogin(id) {
        await (0, database_1.query)(`
      UPDATE admins 
      SET last_login = CURRENT_TIMESTAMP, failed_login_attempts = 0, lock_until = NULL
      WHERE id = $1
    `, [id]);
    }
    /**
     * Count total admins
     */
    static async count() {
        const result = await (0, database_1.query)('SELECT COUNT(*) as count FROM admins');
        return parseInt(result.rows[0].count);
    }
    /**
     * Map database row to AdminData
     */
    static mapRowToAdmin(row) {
        return {
            id: row.id,
            name: row.name,
            email: row.email,
            password: row.password,
            role: row.role,
            privileges: row.privileges ? JSON.parse(row.privileges) : [],
            isMainAdmin: row.is_main_admin,
            resetPasswordToken: row.reset_password_token,
            resetPasswordExpires: row.reset_password_expires,
            failedLoginAttempts: row.failed_login_attempts,
            lockUntil: row.lock_until,
            lastLogin: row.last_login,
            twoFactorSecret: row.two_factor_secret,
            twoFactorEnabled: row.two_factor_enabled,
            twoFactorTempSecret: row.two_factor_temp_secret,
            twoFactorBackupCodes: row.two_factor_backup_codes ? JSON.parse(row.two_factor_backup_codes) : [],
            createdAt: row.created_at,
            updatedAt: row.updated_at,
        };
    }
    /**
     * Convert camelCase to snake_case
     */
    static camelToSnake(str) {
        return str.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
    }
}
exports.Admin = Admin;
