"use strict";
/**
 * PostgreSQL Database Configuration and Connection Module
 *
 * This module provides a clean interface for PostgreSQL database operations
 * with connection pooling and proper error handling.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.initializeDatabase = initializeDatabase;
exports.getDatabase = getDatabase;
exports.query = query;
exports.queryWithClient = queryWithClient;
exports.transaction = transaction;
exports.testConnection = testConnection;
exports.closeDatabase = closeDatabase;
exports.getDatabaseStats = getDatabaseStats;
const pg_1 = require("pg");
const envValidator_1 = require("../utils/envValidator");
// Database connection pool
let pool = null;
/**
 * Get database configuration from environment variables
 */
function getDatabaseConfig() {
    return {
        host: (0, envValidator_1.getEnv)('DB_HOST', 'localhost'),
        port: (0, envValidator_1.getNumEnv)('DB_PORT', 5432),
        database: (0, envValidator_1.getEnv)('DB_NAME', 'mabourse'),
        user: (0, envValidator_1.getEnv)('DB_USER', 'postgres'),
        password: (0, envValidator_1.getEnv)('DB_PASSWORD', 'postgres'),
        ssl: (0, envValidator_1.getBoolEnv)('DB_SSL', false),
        max: (0, envValidator_1.getNumEnv)('DB_POOL_MAX', 20), // Maximum number of clients in the pool
        idleTimeoutMillis: (0, envValidator_1.getNumEnv)('DB_IDLE_TIMEOUT', 30000), // Close idle clients after 30 seconds
        connectionTimeoutMillis: (0, envValidator_1.getNumEnv)('DB_CONNECTION_TIMEOUT', 2000), // Return an error after 2 seconds if connection could not be established
    };
}
/**
 * Initialize database connection pool
 */
function initializeDatabase() {
    if (pool) {
        return pool;
    }
    const config = getDatabaseConfig();
    pool = new pg_1.Pool(config);
    // Handle pool errors
    pool.on('error', (err) => {
        console.error('Unexpected error on idle client', err);
        process.exit(-1);
    });
    // Handle pool connection events
    pool.on('connect', (client) => {
        console.log('New client connected to database');
    });
    pool.on('remove', (client) => {
        console.log('Client removed from pool');
    });
    console.log(`Database pool initialized with max ${config.max} connections`);
    return pool;
}
/**
 * Get database connection pool
 */
function getDatabase() {
    if (!pool) {
        throw new Error('Database not initialized. Call initializeDatabase() first.');
    }
    return pool;
}
/**
 * Execute a query with parameters
 */
async function query(text, params) {
    const db = getDatabase();
    const start = Date.now();
    try {
        const result = await db.query(text, params);
        const duration = Date.now() - start;
        // Log slow queries (over 100ms)
        if (duration > 100) {
            console.warn(`Slow query detected (${duration}ms):`, text.substring(0, 100));
        }
        return result;
    }
    catch (error) {
        console.error('Database query error:', error);
        console.error('Query:', text);
        console.error('Params:', params);
        throw error;
    }
}
/**
 * Execute a query with a specific client (for transactions)
 */
async function queryWithClient(client, text, params) {
    const start = Date.now();
    try {
        const result = await client.query(text, params);
        const duration = Date.now() - start;
        // Log slow queries (over 100ms)
        if (duration > 100) {
            console.warn(`Slow query detected (${duration}ms):`, text.substring(0, 100));
        }
        return result;
    }
    catch (error) {
        console.error('Database query error:', error);
        console.error('Query:', text);
        console.error('Params:', params);
        throw error;
    }
}
/**
 * Execute multiple queries in a transaction
 */
async function transaction(callback) {
    const db = getDatabase();
    const client = await db.connect();
    try {
        await client.query('BEGIN');
        const result = await callback(client);
        await client.query('COMMIT');
        return result;
    }
    catch (error) {
        await client.query('ROLLBACK');
        throw error;
    }
    finally {
        client.release();
    }
}
/**
 * Test database connection
 */
async function testConnection() {
    try {
        const result = await query('SELECT NOW() as current_time');
        console.log('Database connection test successful:', result.rows[0].current_time);
        return true;
    }
    catch (error) {
        console.error('Database connection test failed:', error);
        return false;
    }
}
/**
 * Close database connection pool
 */
async function closeDatabase() {
    if (pool) {
        await pool.end();
        pool = null;
        console.log('Database connection pool closed');
    }
}
/**
 * Get database statistics
 */
async function getDatabaseStats() {
    const db = getDatabase();
    return {
        totalConnections: db.totalCount,
        idleConnections: db.idleCount,
        waitingCount: db.waitingCount,
    };
}
