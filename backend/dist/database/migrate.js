"use strict";
/**
 * Database Migration Script
 *
 * This script handles database schema creation and initial data setup
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.runMigrations = runMigrations;
exports.dropAllTables = dropAllTables;
exports.resetDatabase = resetDatabase;
const fs_1 = __importDefault(require("fs"));
const path_1 = __importDefault(require("path"));
const bcryptjs_1 = __importDefault(require("bcryptjs"));
const dotenv_1 = __importDefault(require("dotenv"));
const database_1 = require("../config/database");
const envValidator_1 = require("../utils/envValidator");
// Load environment variables
dotenv_1.default.config({ path: path_1.default.join(__dirname, '../../.env.development') });
/**
 * Run database migrations
 */
async function runMigrations() {
    console.log('Starting database migrations...');
    console.log('Database config:', {
        host: process.env.DB_HOST,
        port: process.env.DB_PORT,
        database: process.env.DB_NAME,
        user: process.env.DB_USER
    });
    try {
        // Initialize database connection
        (0, database_1.initializeDatabase)();
        // Read and execute schema
        await createSchema();
        // Create initial data
        await createInitialData();
        console.log('Database migrations completed successfully');
    }
    catch (error) {
        console.error('Database migration failed:', error);
        throw error;
    }
}
/**
 * Create database schema
 */
async function createSchema() {
    console.log('Creating database schema...');
    const schemaPath = path_1.default.join(__dirname, 'schema.sql');
    const schema = fs_1.default.readFileSync(schemaPath, 'utf8');
    // Execute the entire schema as one statement
    try {
        await (0, database_1.query)(schema);
    }
    catch (error) {
        console.error('Error executing schema:', error);
        throw error;
    }
    console.log('Database schema created successfully');
}
/**
 * Create initial data (admin user, security settings, etc.)
 */
async function createInitialData() {
    console.log('Creating initial data...');
    // Create main admin if it doesn't exist
    await createMainAdmin();
    // Create default security settings
    await createSecuritySettings();
    console.log('Initial data created successfully');
}
/**
 * Create main admin user
 */
async function createMainAdmin() {
    const adminEmail = (0, envValidator_1.getEnv)('ADMIN_EMAIL', '<EMAIL>');
    const adminPassword = (0, envValidator_1.getEnv)('ADMIN_PASSWORD', 'admin123');
    const adminName = (0, envValidator_1.getEnv)('ADMIN_NAME', 'Main Administrator');
    // Check if main admin already exists
    const existingAdmin = await (0, database_1.query)('SELECT id FROM admins WHERE is_main_admin = TRUE LIMIT 1');
    if (existingAdmin.rows.length > 0) {
        console.log('Main admin already exists, skipping creation');
        return;
    }
    // Hash password
    const hashedPassword = await bcryptjs_1.default.hash(adminPassword, 10);
    // Create main admin
    await (0, database_1.query)(`
    INSERT INTO admins (
      name, email, password, role, privileges, is_main_admin, two_factor_enabled
    ) VALUES ($1, $2, $3, $4, $5, $6, $7)
  `, [
        adminName,
        adminEmail,
        hashedPassword,
        'super_admin',
        JSON.stringify(['all']),
        true,
        false
    ]);
    console.log(`Main admin created: ${adminEmail}`);
}
/**
 * Create default security settings
 */
async function createSecuritySettings() {
    // Check if security settings already exist
    const existingSettings = await (0, database_1.query)('SELECT id FROM security_settings WHERE id = 1');
    if (existingSettings.rows.length > 0) {
        console.log('Security settings already exist, skipping creation');
        return;
    }
    // Create default security settings
    await (0, database_1.query)(`
    INSERT INTO security_settings (
      id, max_login_attempts, lockout_duration, password_expiry_days,
      require_strong_passwords, two_factor_enabled, min_password_length
    ) VALUES ($1, $2, $3, $4, $5, $6, $7)
  `, [1, 5, 30, 90, true, false, 8]);
    console.log('Default security settings created');
}
/**
 * Drop all tables (for development/testing)
 */
async function dropAllTables() {
    console.log('Dropping all tables...');
    const tables = [
        'password_history',
        'security_events',
        'newsletter_subscriptions',
        'messages',
        'scholarships',
        'admins',
        'users',
        'security_settings'
    ];
    for (const table of tables) {
        try {
            await (0, database_1.query)(`DROP TABLE IF EXISTS ${table} CASCADE`);
            console.log(`Dropped table: ${table}`);
        }
        catch (error) {
            console.error(`Error dropping table ${table}:`, error);
        }
    }
    // Drop triggers and functions
    try {
        await (0, database_1.query)('DROP FUNCTION IF EXISTS update_updated_at_column() CASCADE');
        console.log('Dropped update_updated_at_column function');
    }
    catch (error) {
        console.error('Error dropping function:', error);
    }
    console.log('All tables dropped');
}
/**
 * Reset database (drop and recreate)
 */
async function resetDatabase() {
    console.log('Resetting database...');
    await dropAllTables();
    await runMigrations();
    console.log('Database reset completed');
}
// CLI interface
if (require.main === module) {
    const command = process.argv[2];
    async function main() {
        try {
            switch (command) {
                case 'migrate':
                    await runMigrations();
                    break;
                case 'reset':
                    await resetDatabase();
                    break;
                case 'drop':
                    await dropAllTables();
                    break;
                default:
                    console.log('Usage: ts-node migrate.ts [migrate|reset|drop]');
                    process.exit(1);
            }
        }
        catch (error) {
            console.error('Migration script failed:', error);
            process.exit(1);
        }
        finally {
            await (0, database_1.closeDatabase)();
        }
    }
    main();
}
