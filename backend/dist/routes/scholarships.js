"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = require("express");
const Scholarship_1 = require("../models/Scholarship");
const router = (0, express_1.Router)();
// Get all scholarships
router.get('/', async (req, res) => {
    try {
        const scholarships = await Scholarship_1.Scholarship.findAll();
        res.json(scholarships);
    }
    catch (error) {
        res.status(500).json({ error: 'Error fetching scholarships' });
    }
});
// Get a single scholarship
router.get('/:id', async (req, res) => {
    try {
        const { id } = req.params;
        const scholarship = await Scholarship_1.Scholarship.findById(Number(id));
        if (!scholarship) {
            return res.status(404).json({ error: 'Scholarship not found' });
        }
        res.json(scholarship);
    }
    catch (error) {
        res.status(500).json({ error: 'Error fetching scholarship' });
    }
});
// Create a new scholarship
router.post('/', async (req, res) => {
    try {
        const { title, description, amount, deadline, requirements, status } = req.body;
        const scholarship = await Scholarship_1.Scholarship.create({
            title,
            description,
            deadline: new Date(deadline),
            isOpen: status !== 'closed',
            createdBy: 1 // Default admin user
        });
        res.json(scholarship);
    }
    catch (error) {
        res.status(500).json({ error: 'Error creating scholarship' });
    }
});
// Update a scholarship
router.put('/:id', async (req, res) => {
    try {
        const { id } = req.params;
        const { title, description, amount, deadline, requirements, status } = req.body;
        const scholarship = await Scholarship_1.Scholarship.update(Number(id), {
            title,
            description,
            deadline: new Date(deadline),
            isOpen: status !== 'closed'
        });
        res.json(scholarship);
    }
    catch (error) {
        res.status(500).json({ error: 'Error updating scholarship' });
    }
});
// Delete a scholarship
router.delete('/:id', async (req, res) => {
    try {
        const { id } = req.params;
        await Scholarship_1.Scholarship.delete(Number(id));
        res.json({ message: 'Scholarship deleted successfully' });
    }
    catch (error) {
        res.status(500).json({ error: 'Error deleting scholarship' });
    }
});
exports.default = router;
