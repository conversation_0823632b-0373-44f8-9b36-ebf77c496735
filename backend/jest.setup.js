// This file contains setup code for Jest tests

// Mock Express Request and Response objects
jest.mock('express', () => {
  const mockRequest = () => {
    const req = {};
    req.body = jest.fn().mockReturnValue(req);
    req.params = jest.fn().mockReturnValue(req);
    req.query = jest.fn().mockReturnValue(req);
    req.user = jest.fn().mockReturnValue(req);
    return req;
  };

  const mockResponse = () => {
    const res = {};
    res.status = jest.fn().mockReturnValue(res);
    res.json = jest.fn().mockReturnValue(res);
    res.send = jest.fn().mockReturnValue(res);
    return res;
  };

  return {
    Request: mockRequest,
    Response: mockResponse,
  };
});

// Mock bcryptjs
jest.mock('bcryptjs', () => ({
  hash: jest.fn().mockResolvedValue('hashed_password'),
  compare: jest.fn().mockResolvedValue(true),
  genSalt: jest.fn().mockResolvedValue('salt'),
}));

// Mock jsonwebtoken
jest.mock('jsonwebtoken', () => ({
  sign: jest.fn().mockReturnValue('mock_token'),
  verify: jest.fn().mockReturnValue({ id: 1, email: '<EMAIL>', role: 'user', isMainAdmin: false }),
}));

// Mock PostgreSQL database connection
jest.mock('../src/config/database', () => ({
  initializeDatabase: jest.fn(),
  testConnection: jest.fn().mockResolvedValue(true),
  closeDatabase: jest.fn(),
  query: jest.fn(),
  transaction: jest.fn(),
}));

// Mock express-validator
jest.mock('express-validator', () => ({
  validationResult: jest.fn().mockImplementation(() => ({
    isEmpty: jest.fn().mockReturnValue(true),
    array: jest.fn().mockReturnValue([]),
  })),
  body: jest.fn().mockReturnThis(),
  check: jest.fn().mockReturnThis(),
  param: jest.fn().mockReturnThis(),
  query: jest.fn().mockReturnThis(),
  isEmail: jest.fn().mockReturnThis(),
  isLength: jest.fn().mockReturnThis(),
  isDate: jest.fn().mockReturnThis(),
  isURL: jest.fn().mockReturnThis(),
  isBoolean: jest.fn().mockReturnThis(),
  isIn: jest.fn().mockReturnThis(),
  optional: jest.fn().mockReturnThis(),
  withMessage: jest.fn().mockReturnThis(),
}));
